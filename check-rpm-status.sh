#!/bin/bash

# <PERSON>ript to check vSRX RPM status and troubleshoot latency data collection
VSRX_IP="**************"

echo "🔍 vSRX RPM Status Check"
echo "========================"
echo "vSRX IP: $VSRX_IP"
echo "Dashboard: http://localhost:3000/d/vsrx-rpm-latency/vsrx-rpm-latency-monitoring"
echo ""

# Check SNMP connectivity
echo "📡 Testing SNMP connectivity..."
SNMP_RESULT=$(curl -s "http://localhost:9116/snmp?target=$VSRX_IP&module=juniper_rpm_samples&auth=public_v2" | grep "snmp_scrape_pdus_returned")
if [ ! -z "$SNMP_RESULT" ]; then
    PDU_COUNT=$(echo "$SNMP_RESULT" | grep -o '[0-9]\+' | tail -1)
    echo "✅ SNMP working - Found $PDU_COUNT PDUs"
    
    if [ "$PDU_COUNT" -gt 2 ]; then
        echo "✅ RPM data detected!"
    else
        echo "⚠️  Only system metrics found (no RPM data yet)"
    fi
else
    echo "❌ SNMP connection failed"
    exit 1
fi

echo ""

# Check Prometheus target health
echo "📊 Checking Prometheus target..."
TARGET_HEALTH=$(curl -s "http://localhost:9090/api/v1/targets" | jq -r '.data.activeTargets[] | select(.discoveredLabels.__address__ == "'$VSRX_IP'") | .health')
if [ "$TARGET_HEALTH" = "up" ]; then
    echo "✅ Prometheus target healthy"
else
    echo "❌ Prometheus target unhealthy: $TARGET_HEALTH"
fi

echo ""

# Check for RPM metrics in Prometheus
echo "🎯 Checking for RPM metrics in Prometheus..."
RPM_METRICS=$(curl -s "http://localhost:9090/api/v1/query?query=jnxRpmResRtt" | jq '.data.result | length')
if [ "$RPM_METRICS" -gt 0 ]; then
    echo "✅ Found $RPM_METRICS RPM latency metrics in Prometheus"
else
    echo "⚠️  No RPM latency metrics found in Prometheus yet"
fi

echo ""

# Check system metrics
echo "🖥️  Checking vSRX system status..."
UPTIME=$(curl -s "http://localhost:9090/api/v1/query?query=sysUpTime{instance=\"$VSRX_IP\"}" | jq -r '.data.result[0].value[1] // "null"')
if [ "$UPTIME" != "null" ]; then
    UPTIME_DAYS=$(echo "scale=1; $UPTIME / 100 / 86400" | bc -l 2>/dev/null || echo "N/A")
    echo "✅ vSRX uptime: $UPTIME_DAYS days"
else
    echo "⚠️  vSRX system metrics not available"
fi

echo ""
echo "🔧 Troubleshooting Steps:"
echo "========================"

if [ "$PDU_COUNT" -le 2 ]; then
    echo "1. Check if RPM probes are running on vSRX:"
    echo "   ssh admin@$VSRX_IP"
    echo "   show services rpm probe-results"
    echo "   show services rpm probe-results summary"
    echo ""
    echo "2. If no results, restart RPM service:"
    echo "   restart services rpm"
    echo ""
    echo "3. Check RPM configuration:"
    echo "   show configuration services rpm"
    echo ""
fi

echo "4. Monitor dashboard for updates:"
echo "   http://localhost:3000/d/vsrx-rpm-latency/vsrx-rpm-latency-monitoring"
echo ""

echo "5. Expected probe names:"
echo "   - wifi-probe → *******"
echo "   - info-probe → *******"
echo "   - orfr-probe → *******"
echo "   - gm-probe → *******"
echo "   - ee-probe → *******"
echo "   - vf-probe → *******"
