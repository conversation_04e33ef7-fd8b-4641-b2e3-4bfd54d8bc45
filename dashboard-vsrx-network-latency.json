{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Real-time latency measurements from vSRX RPM probes across all 6 networks to multiple targets", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "µs"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*wifi.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*info.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*orfr.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*gm.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*ee.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "purple", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*vf.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "wifi_googl_rtt or info_googl_rtt or orfr_googl_rtt or gm_googl_rtt or ee_googl_rtt or vf_googl_rtt", "interval": "", "legendFormat": "{{__name__}}", "refId": "A"}], "title": "vSRX RPM Latency to Google DNS (*******) - All Network Probes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Current latency values for each network probe to Google DNS", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 500000, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100000}, {"color": "red", "value": 200000}]}, "unit": "µs"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "id": 2, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "10.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "wifi_googl_rtt or info_googl_rtt or orfr_googl_rtt or gm_googl_rtt or ee_googl_rtt or vf_googl_rtt", "interval": "", "legendFormat": "{{__name__}}", "refId": "A"}], "title": "Current Latency by Network Probe", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Statistics table showing current latency for each network probe to Google DNS", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100000}, {"color": "red", "value": 200000}]}, "unit": "µs"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Network"}, "properties": [{"id": "custom.width", "value": 120}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "id": 3, "options": {"showHeader": true}, "pluginVersion": "10.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "wifi_googl_rtt or info_googl_rtt or orfr_googl_rtt or gm_googl_rtt or ee_googl_rtt or vf_googl_rtt", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Google DNS Latency Statistics Table", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "instance": true, "job": true}, "indexByName": {}, "renameByName": {"Value": "Current Latency (µs)", "__name__": "Network"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Latency comparison across all targets for each network", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "µs"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*FW.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*gw.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*cflare.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*googl.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 18}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "wifi_FW_rtt or wifi_gw_rtt or wifi_cflare_rtt or wifi_googl_rtt or info_FW_rtt or info_gw_rtt or info_cflare_rtt or info_googl_rtt or orfr_FW_rtt or orfr_gw_rtt or orfr_cflare_rtt or orfr_googl_rtt or gm_FW_rtt or gm_gw_rtt or gm_cflare_rtt or gm_googl_rtt or ee_FW_rtt or ee_gw_rtt or ee_cflare_rtt or ee_googl_rtt or vf_FW_rtt or vf_gw_rtt or vf_cflare_rtt or vf_googl_rtt", "interval": "", "legendFormat": "{{__name__}}", "refId": "A"}], "title": "All Targets Latency - Multi-Target Comparison", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "vSRX system health and SNMP collection performance", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 80}, {"color": "red", "value": 90}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 12, "y": 18}, "id": 5, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sysUpTime{instance=\"**************\"} / 100", "legendFormat": "vSRX Uptime", "range": true, "refId": "A"}], "title": "vSRX System Uptime", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "SNMP collection performance and data availability", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 12, "y": 22}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "snmp_scrape_pdus_returned{instance=\"**************\"}", "legendFormat": "SNMP PDUs Collected", "range": true, "refId": "A"}], "title": "SNMP Data Collection Status", "type": "timeseries"}], "refresh": "5s", "schemaVersion": 41, "style": "dark", "tags": ["vsrx", "rpm", "latency", "network", "probes", "juniper"], "templating": {"list": []}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "vSRX Network Latency Dashboard - RPM Probes", "uid": "vsrx-network-latency", "version": 1}