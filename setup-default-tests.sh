#!/bin/bash

# Setup script for Synthetic Monitoring System
# This script helps configure default tests and verify the setup

echo "=== Synthetic Monitoring Setup ==="
echo

# Check if Docker networks exist
echo "Checking Docker networks..."
NETWORKS=("ee" "gm" "info" "orfr" "wifi" "vf")

for network in "${NETWORKS[@]}"; do
    if docker network ls | grep -q "$network"; then
        echo "✓ Network '$network' exists"
    else
        echo "⚠ Network '$network' does not exist. Creating..."
        docker network create "$network"
    fi
done

echo

# Build containers
echo "Building containers..."
echo "Building master container..."
docker build -t synthetic-master ./synthetic-monitor-master/

echo "Building worker container..."
docker build -t synthetic-worker ./synthetic-monitor-worker/

echo

# Start the system
echo "Starting synthetic monitoring system..."
docker-compose up -d

echo

# Wait for services to start
echo "Waiting for services to start..."
sleep 30

# Check service health
echo "Checking service health..."

# Check Prometheus
if curl -s http://localhost:9090/-/healthy > /dev/null; then
    echo "✓ Prometheus is healthy"
else
    echo "⚠ Prometheus is not responding"
fi

# Check Grafana
if curl -s http://localhost:3000/api/health > /dev/null; then
    echo "✓ Grafana is healthy"
else
    echo "⚠ Grafana is not responding"
fi

# Check Master
if curl -s http://localhost:8080/health > /dev/null; then
    echo "✓ Master container is healthy"
else
    echo "⚠ Master container is not responding"
fi

echo

# Configure default tests via API
echo "Configuring default tests..."

# Default configuration with ******* tests
DEFAULT_CONFIG='{
  "workers": {
    "ee": {"enabled": true, "targets": ["*******"]},
    "gm": {"enabled": true, "targets": ["*******"]},
    "info": {"enabled": true, "targets": ["*******"]},
    "orfr": {"enabled": true, "targets": ["*******"]},
    "wifi": {"enabled": true, "targets": ["*******"]},
    "vf": {"enabled": true, "targets": ["*******"]}
  },
  "tests": {
    "ping": {
      "enabled": true,
      "interval": 3,
      "timeout": 2
    },
    "traceroute": {
      "enabled": true,
      "interval": 300,
      "timeout": 30
    }
  }
}'

# Send configuration to master
curl -X POST \
  -H "Content-Type: application/json" \
  -d "$DEFAULT_CONFIG" \
  http://localhost:8080/api/config

echo
echo "=== Setup Complete ==="
echo
echo "Access URLs:"
echo "- Grafana: http://localhost:3000 (admin/admin)"
echo "- Prometheus: http://localhost:9090"
echo "- Master Web UI: http://localhost:8080"
echo
echo "The system is now monitoring connectivity to ******* from all 6 networks:"
echo "- Ping tests every 3 seconds"
echo "- Traceroute tests every 5 minutes"
echo
echo "Check the Grafana dashboard for real-time metrics!"
