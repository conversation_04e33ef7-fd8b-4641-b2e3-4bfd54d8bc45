# Complete SNMP configuration for Juniper vSRX RPM - All 24 metrics
# Using correct OID structure: *******.4.1.2636.********.1.2.1.{probe}.1.{test}.1
# Probe mapping: 1=wifi(49), 2=info(50), 3=orfr(51), 4=gm(52), 5=ee(53), 6=vf(54)
# Test mapping: 1=FW(49), 2=gw(51), 3=cflare(52), 4=googl(53)
auths:
  public_v2:
    community: vsrx_probe
    security_level: noAuthNoPriv
    version: 2

modules:
  # Complete RPM configuration with all 24 specific metrics
  juniper_rpm_complete:
    walk:
      # System info
      - *******.*******.0    # sysDescr
      - *******.*******.0    # sysUpTime

      # All 24 specific RPM RTT OIDs
      # Probe 1 (wifi) - OID base 49
      - *******.4.1.2636.********.********.1.49.1    # wifi_FW_rtt
      - *******.4.1.2636.********.********.1.51.1    # wifi_gw_rtt
      - *******.4.1.2636.********.********.1.52.1    # wifi_cflare_rtt
      - *******.4.1.2636.********.********.1.53.1    # wifi_googl_rtt

      # Probe 2 (info) - OID base 50
      - *******.4.1.2636.********.********.1.49.1    # info_FW_rtt
      - *******.4.1.2636.********.********.1.51.1    # info_gw_rtt
      - *******.4.1.2636.********.********.1.52.1    # info_cflare_rtt
      - *******.4.1.2636.********.********.1.53.1    # info_googl_rtt

      # Probe 3 (orfr) - OID base 51
      - *******.4.1.2636.********.********.1.49.1    # orfr_FW_rtt
      - *******.4.1.2636.********.********.1.51.1    # orfr_gw_rtt
      - *******.4.1.2636.********.********.1.52.1    # orfr_cflare_rtt
      - *******.4.1.2636.********.********.1.53.1    # orfr_googl_rtt

      # Probe 4 (gm) - OID base 52
      - *******.4.1.2636.********.1.2.1.52.1.49.1    # gm_FW_rtt
      - *******.4.1.2636.********.1.2.1.52.1.51.1    # gm_gw_rtt
      - *******.4.1.2636.********.1.2.1.52.1.52.1    # gm_cflare_rtt
      - *******.4.1.2636.********.1.2.1.52.1.53.1    # gm_googl_rtt

      # Probe 5 (ee) - OID base 53
      - *******.4.1.2636.********.********.1.49.1    # ee_FW_rtt
      - *******.4.1.2636.********.********.1.51.1    # ee_gw_rtt
      - *******.4.1.2636.********.********.1.52.1    # ee_cflare_rtt
      - *******.4.1.2636.********.********.1.53.1    # ee_googl_rtt

      # Probe 6 (vf) - OID base 54
      - *******.4.1.2636.********.********.1.49.1    # vf_FW_rtt
      - *******.4.1.2636.********.********.1.51.1    # vf_gw_rtt
      - *******.4.1.2636.********.********.1.52.1    # vf_cflare_rtt
      - *******.4.1.2636.********.********.1.53.1    # vf_googl_rtt

    metrics:
      # System metrics
      - name: sysDescr
        oid: *******.*******.0
        type: DisplayString
        help: System description
      - name: sysUpTime
        oid: *******.*******.0
        type: gauge
        help: System uptime in hundredths of seconds

      # Probe 1 (wifi) RPM Metrics
      - name: wifi_FW_rtt
        oid: *******.4.1.2636.********.********.1.49.1
        type: gauge
        help: WiFi network RTT to firewall (***********) in microseconds
      - name: wifi_gw_rtt
        oid: *******.4.1.2636.********.********.1.51.1
        type: gauge
        help: WiFi network RTT to gateway (*******) in microseconds
      - name: wifi_cflare_rtt
        oid: *******.4.1.2636.********.********.1.52.1
        type: gauge
        help: WiFi network RTT to Cloudflare (*************) in microseconds
      - name: wifi_googl_rtt
        oid: *******.4.1.2636.********.********.1.53.1
        type: gauge
        help: WiFi network RTT to Google (*******) in microseconds

      # Probe 2 (info) RPM Metrics
      - name: info_FW_rtt
        oid: *******.4.1.2636.********.********.1.49.1
        type: gauge
        help: Info network RTT to firewall (172.30.1.142) in microseconds
      - name: info_gw_rtt
        oid: *******.4.1.2636.********.********.1.51.1
        type: gauge
        help: Info network RTT to gateway (2.2.2.2) in microseconds
      - name: info_cflare_rtt
        oid: *******.4.1.2636.********.********.1.52.1
        type: gauge
        help: Info network RTT to Cloudflare (*************) in microseconds
      - name: info_googl_rtt
        oid: *******.4.1.2636.********.********.1.53.1
        type: gauge
        help: Info network RTT to Google (*******) in microseconds

      # Probe 3 (orfr) RPM Metrics
      - name: orfr_FW_rtt
        oid: *******.4.1.2636.********.********.1.49.1
        type: gauge
        help: ORFR network RTT to firewall (172.30.1.142) in microseconds
      - name: orfr_gw_rtt
        oid: *******.4.1.2636.********.********.1.51.1
        type: gauge
        help: ORFR network RTT to gateway (2.2.2.2) in microseconds
      - name: orfr_cflare_rtt
        oid: *******.4.1.2636.********.********.1.52.1
        type: gauge
        help: ORFR network RTT to Cloudflare (*************) in microseconds
      - name: orfr_googl_rtt
        oid: *******.4.1.2636.********.********.1.53.1
        type: gauge
        help: ORFR network RTT to Google (*******) in microseconds

      # Probe 4 (gm) RPM Metrics
      - name: gm_FW_rtt
        oid: *******.4.1.2636.********.1.2.1.52.1.49.1
        type: gauge
        help: GM network RTT to firewall (172.30.1.142) in microseconds
      - name: gm_gw_rtt
        oid: *******.4.1.2636.********.1.2.1.52.1.51.1
        type: gauge
        help: GM network RTT to gateway (2.2.2.2) in microseconds
      - name: gm_cflare_rtt
        oid: *******.4.1.2636.********.1.2.1.52.1.52.1
        type: gauge
        help: GM network RTT to Cloudflare (*************) in microseconds
      - name: gm_googl_rtt
        oid: *******.4.1.2636.********.1.2.1.52.1.53.1
        type: gauge
        help: GM network RTT to Google (*******) in microseconds

      # Probe 5 (ee) RPM Metrics
      - name: ee_FW_rtt
        oid: *******.4.1.2636.********.********.1.49.1
        type: gauge
        help: EE network RTT to firewall (172.30.1.142) in microseconds
      - name: ee_gw_rtt
        oid: *******.4.1.2636.********.********.1.51.1
        type: gauge
        help: EE network RTT to gateway (2.2.2.2) in microseconds
      - name: ee_cflare_rtt
        oid: *******.4.1.2636.********.********.1.52.1
        type: gauge
        help: EE network RTT to Cloudflare (*************) in microseconds
      - name: ee_googl_rtt
        oid: *******.4.1.2636.********.********.1.53.1
        type: gauge
        help: EE network RTT to Google (*******) in microseconds

      # Probe 6 (vf) RPM Metrics
      - name: vf_FW_rtt
        oid: *******.4.1.2636.********.********.1.49.1
        type: gauge
        help: VF network RTT to firewall (172.30.1.142) in microseconds
      - name: vf_gw_rtt
        oid: *******.4.1.2636.********.********.1.51.1
        type: gauge
        help: VF network RTT to gateway (2.2.2.2) in microseconds
      - name: vf_cflare_rtt
        oid: *******.4.1.2636.********.********.1.52.1
        type: gauge
        help: VF network RTT to Cloudflare (*************) in microseconds
      - name: vf_googl_rtt
        oid: *******.4.1.2636.********.********.1.53.1
        type: gauge
        help: VF network RTT to Google (*******) in microseconds
