<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Synthetic Monitor - Probe Configuration</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8em;
            font-weight: 400;
        }

        .form-container {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            border-left: 4px solid #4facfe;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #48cae4 0%, #023047 100%);
        }

        .probes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 25px;
        }

        .probe-card {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .probe-card:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .probe-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .probe-card.disabled {
            opacity: 0.6;
        }

        .probe-card.disabled:before {
            background: #ccc;
        }

        .probe-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .probe-name {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
        }

        .probe-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-enabled {
            background: #d4edda;
            color: #155724;
        }

        .status-disabled {
            background: #f8d7da;
            color: #721c24;
        }

        .probe-details {
            margin-bottom: 20px;
        }

        .probe-detail {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            color: #666;
        }

        .probe-actions {
            display: flex;
            gap: 10px;
        }

        .probe-actions .btn {
            flex: 1;
            text-align: center;
            padding: 8px 15px;
            font-size: 14px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            color: #ddd;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e1e5e9;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: 700;
            color: #4facfe;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .probes-grid {
                grid-template-columns: 1fr;
            }
            
            .probe-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Synthetic Monitor</h1>
            <p>Configure and manage ICMP ping probes with real-time monitoring</p>
        </div>

        <div class="content">
            <!-- Statistics -->
            <div class="stats-container">
                <div class="stat-card">
                    <div class="stat-number">{{ probes|length }}</div>
                    <div class="stat-label">Total Probes</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ probes|selectattr('enabled')|list|length }}</div>
                    <div class="stat-label">Active Probes</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ (probes|selectattr('enabled')|list|length / (probes|length) * 100)|round(1) if probes|length > 0 else 0 }}%</div>
                    <div class="stat-label">Uptime</div>
                </div>
            </div>

            <!-- Add New Probe Form -->
            <div class="section">
                <h2>➕ Add New Probe</h2>
                <div class="form-container">
                    <form method="POST" action="/add_probe">
                        <div class="form-group">
                            <label for="name">Probe Name:</label>
                            <input type="text" id="name" name="name" required placeholder="e.g., Google DNS">
                        </div>
                        <div class="form-group">
                            <label for="target_ip">Target IP Address:</label>
                            <input type="text" id="target_ip" name="target_ip" required placeholder="e.g., *******" pattern="^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$">
                        </div>
                        <div class="form-group">
                            <label for="interval">Ping Interval (seconds):</label>
                            <select id="interval" name="interval">
                                <option value="30">30 seconds</option>
                                <option value="60" selected>1 minute</option>
                                <option value="300">5 minutes</option>
                                <option value="600">10 minutes</option>
                                <option value="1800">30 minutes</option>
                            </select>
                        </div>
                        <button type="submit" class="btn">Add Probe</button>
                    </form>
                </div>
            </div>

            <!-- Existing Probes -->
            <div class="section">
                <h2>📊 Configured Probes</h2>
                {% if probes %}
                    <div class="probes-grid">
                        {% for probe in probes %}
                            <div class="probe-card {% if not probe.enabled %}disabled{% endif %}">
                                <div class="probe-header">
                                    <div class="probe-name">{{ probe.name }}</div>
                                    <div class="probe-status {% if probe.enabled %}status-enabled{% else %}status-disabled{% endif %}">
                                        {% if probe.enabled %}Enabled{% else %}Disabled{% endif %}
                                    </div>
                                </div>
                                <div class="probe-details">
                                    <div class="probe-detail">
                                        <span>Target IP:</span>
                                        <span><strong>{{ probe.target_ip }}</strong></span>
                                    </div>
                                    <div class="probe-detail">
                                        <span>Interval:</span>
                                        <span>{{ probe.interval }}s</span>
                                    </div>
                                </div>
                                <div class="probe-actions">
                                    <form method="POST" action="/toggle_probe/{{ probe.target_ip }}" style="flex: 1;">
                                        <button type="submit" class="btn {% if probe.enabled %}btn-warning{% else %}btn-success{% endif %}">
                                            {% if probe.enabled %}Disable{% else %}Enable{% endif %}
                                        </button>
                                    </form>
                                    <form method="POST" action="/delete_probe/{{ probe.target_ip }}" style="flex: 1;" onsubmit="return confirm('Are you sure you want to delete this probe?')">
                                        <button type="submit" class="btn btn-danger">Delete</button>
                                    </form>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="empty-state">
                        <div style="font-size: 4em; margin-bottom: 20px;">📡</div>
                        <h3>No probes configured yet</h3>
                        <p>Add your first probe using the form above to start monitoring!</p>
                    </div>
                {% endif %}
            </div>

            <!-- Quick Links -->
            <div class="section">
                <h2>🔗 Quick Links</h2>
                <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                    <a href="/metrics" class="btn" target="_blank">📈 Prometheus Metrics</a>
                    <a href="/api/probes" class="btn" target="_blank">🔌 API Endpoint</a>
                    <a href="/health" class="btn" target="_blank">❤️ Health Check</a>
                    <a href="http://localhost:3000" class="btn" target="_blank">📊 Grafana Dashboard</a>
                    <a href="http://localhost:9090" class="btn" target="_blank">🎯 Prometheus UI</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh page every 30 seconds to show updated probe status
        setTimeout(function() {
            location.reload();
        }, 30000);

        // Form validation
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form[action="/add_probe"]');
            const ipInput = document.getElementById('target_ip');
            
            form.addEventListener('submit', function(e) {
                const ipPattern = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
                if (!ipPattern.test(ipInput.value)) {
                    e.preventDefault();
                    alert('Please enter a valid IP address (e.g., ***********)');
                    ipInput.focus();
                }
            });
        });
    </script>
</body>
</html>
