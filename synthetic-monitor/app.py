import os
import json
import time
import threading
import subprocess
import yaml
from flask import Flask, render_template, request, jsonify, redirect, url_for
from prometheus_client import Counter, Histogram, Gauge, generate_latest, CONTENT_TYPE_LATEST
from opentelemetry import trace, metrics
from opentelemetry.exporter.otlp.proto.http.metric_exporter import OTLPMetricExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# OpenTelemetry setup
trace.set_tracer_provider(TracerProvider())
tracer = trace.get_tracer(__name__)

# Metrics setup
otlp_exporter = OTLPMetricExporter(
    endpoint=os.getenv("OTEL_ENDPOINT", "http://localhost:4318") + "/v1/metrics"
)
metric_reader = PeriodicExportingMetricReader(otlp_exporter, export_interval_millis=5000)
metrics.set_meter_provider(MeterProvider(metric_readers=[metric_reader]))
meter = metrics.get_meter(__name__)

# Prometheus metrics
ping_rtt_histogram = Histogram('ping_rtt_seconds', 'RTT of ICMP ping in seconds', ['target'])
ping_success_counter = Counter('ping_success_total', 'Total successful pings', ['target'])
ping_failure_counter = Counter('ping_failure_total', 'Total failed pings', ['target'])
ping_packet_loss = Gauge('ping_packet_loss_percent', 'Packet loss percentage', ['target'])

# OpenTelemetry metrics
otel_ping_rtt = meter.create_histogram(
    name="ping_rtt_seconds",
    description="RTT of ICMP ping in seconds",
    unit="s"
)
otel_ping_success = meter.create_counter(
    name="ping_success_total",
    description="Total successful pings"
)
otel_ping_failure = meter.create_counter(
    name="ping_failure_total", 
    description="Total failed pings"
)

# Configuration storage
CONFIG_FILE = '/app/config/probes.json'
probes_config = []
active_threads = {}

def load_config():
    global probes_config
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r') as f:
                probes_config = json.load(f)
        else:
            probes_config = []
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        probes_config = []

def save_config():
    try:
        os.makedirs(os.path.dirname(CONFIG_FILE), exist_ok=True)
        with open(CONFIG_FILE, 'w') as f:
            json.dump(probes_config, f, indent=2)
    except Exception as e:
        logger.error(f"Error saving config: {e}")

def ping_target(target_ip, count=1):
    """Perform ICMP ping and return RTT and packet loss"""
    try:
        cmd = ['ping', '-c', str(count), target_ip]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            # Parse ping output for RTT
            lines = result.stdout.split('\n')
            rtt_line = None
            for line in lines:
                if 'round-trip' in line or 'rtt' in line:
                    rtt_line = line
                    break
            
            if rtt_line:
                # Extract average RTT (format varies by OS)
                parts = rtt_line.split('=')[-1].strip()
                avg_rtt = float(parts.split('/')[1]) / 1000.0  # Convert ms to seconds
                return avg_rtt, 0.0
            else:
                return None, 0.0
        else:
            return None, 100.0
            
    except Exception as e:
        logger.error(f"Ping error for {target_ip}: {e}")
        return None, 100.0

def probe_worker(probe_config):
    """Worker thread for continuous probing"""
    target_ip = probe_config['target_ip']
    interval = probe_config['interval']
    name = probe_config['name']
    
    logger.info(f"Starting probe worker for {name} ({target_ip})")
    
    while target_ip in active_threads:
        try:
            rtt, packet_loss = ping_target(target_ip)
            
            if rtt is not None:
                # Prometheus metrics
                ping_rtt_histogram.labels(target=target_ip).observe(rtt)
                ping_success_counter.labels(target=target_ip).inc()
                
                # OpenTelemetry metrics
                otel_ping_rtt.record(rtt, {"target": target_ip, "probe_name": name})
                otel_ping_success.add(1, {"target": target_ip, "probe_name": name})
                
                logger.info(f"Ping to {target_ip}: RTT={rtt:.3f}s")
            else:
                # Prometheus metrics
                ping_failure_counter.labels(target=target_ip).inc()
                
                # OpenTelemetry metrics
                otel_ping_failure.add(1, {"target": target_ip, "probe_name": name})
                
                logger.warning(f"Ping to {target_ip}: FAILED")
            
            # Update packet loss metric
            ping_packet_loss.labels(target=target_ip).set(packet_loss)
            
        except Exception as e:
            logger.error(f"Error in probe worker for {target_ip}: {e}")
        
        time.sleep(interval)

def start_probe(probe_config):
    """Start a probe thread"""
    target_ip = probe_config['target_ip']
    if target_ip not in active_threads:
        thread = threading.Thread(target=probe_worker, args=(probe_config,))
        thread.daemon = True
        active_threads[target_ip] = thread
        thread.start()
        logger.info(f"Started probe for {target_ip}")

def stop_probe(target_ip):
    """Stop a probe thread"""
    if target_ip in active_threads:
        del active_threads[target_ip]
        logger.info(f"Stopped probe for {target_ip}")

@app.route('/')
def index():
    return render_template('index.html', probes=probes_config)

@app.route('/add_probe', methods=['POST'])
def add_probe():
    name = request.form.get('name')
    target_ip = request.form.get('target_ip')
    interval = int(request.form.get('interval', 60))
    
    if not name or not target_ip:
        return jsonify({'error': 'Name and target IP are required'}), 400
    
    # Check if probe already exists
    for probe in probes_config:
        if probe['target_ip'] == target_ip:
            return jsonify({'error': 'Probe for this IP already exists'}), 400
    
    probe_config = {
        'name': name,
        'target_ip': target_ip,
        'interval': interval,
        'enabled': True
    }
    
    probes_config.append(probe_config)
    save_config()
    start_probe(probe_config)
    
    return redirect(url_for('index'))

@app.route('/toggle_probe/<target_ip>', methods=['POST'])
def toggle_probe(target_ip):
    for probe in probes_config:
        if probe['target_ip'] == target_ip:
            probe['enabled'] = not probe['enabled']
            if probe['enabled']:
                start_probe(probe)
            else:
                stop_probe(target_ip)
            save_config()
            break
    
    return redirect(url_for('index'))

@app.route('/delete_probe/<target_ip>', methods=['POST'])
def delete_probe(target_ip):
    global probes_config
    stop_probe(target_ip)
    probes_config = [p for p in probes_config if p['target_ip'] != target_ip]
    save_config()
    return redirect(url_for('index'))

@app.route('/metrics')
def metrics():
    """Prometheus metrics endpoint"""
    return generate_latest(), 200, {'Content-Type': CONTENT_TYPE_LATEST}

@app.route('/api/probes')
def api_probes():
    """API endpoint to get all probes"""
    return jsonify(probes_config)

@app.route('/health')
def health():
    return jsonify({'status': 'healthy', 'active_probes': len(active_threads)})

if __name__ == '__main__':
    # Load configuration
    load_config()
    
    # Start enabled probes
    for probe in probes_config:
        if probe.get('enabled', True):
            start_probe(probe)
    
    # Run Flask app
    app.run(host='0.0.0.0', port=8080, debug=True)
