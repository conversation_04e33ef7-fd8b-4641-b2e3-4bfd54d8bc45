<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Synthetic Monitoring - Traceroute Visualization</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .controls {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .panel h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        #network-graph {
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .hop-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .hop-table th, .hop-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .hop-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .hop-table tr:hover {
            background-color: #f5f5f5;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 10px;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        select, button {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        button {
            background: #667eea;
            color: white;
            cursor: pointer;
            border: none;
        }
        button:hover {
            background: #5a6fd8;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .nav-bar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 15px 0;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
        }
        .nav-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .nav-title {
            color: white;
            font-size: 1.5em;
            font-weight: bold;
            margin: 0;
        }
        .nav-links {
            display: flex;
            gap: 20px;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .nav-links a:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-bar">
            <div class="nav-content">
                <h1 class="nav-title">🌐 Synthetic Monitor</h1>
                <div class="nav-links">
                    <a href="/">📊 Dashboard</a>
                    <a href="/traceroute" class="active">🗺️ Traceroute</a>
                    <a href="http://localhost:3000" target="_blank">📈 Grafana</a>
                    <a href="http://localhost:9090" target="_blank">🔍 Prometheus</a>
                </div>
            </div>
        </div>

        <div class="header">
            <h1>🗺️ Traceroute Visualization</h1>
            <p>Real-time network path analysis and performance monitoring</p>
        </div>

        <div class="controls">
            <label for="networkSelect">Network:</label>
            <select id="networkSelect">
                <option value="">All Networks</option>
            </select>
            
            <label for="targetSelect">Target:</label>
            <select id="targetSelect">
                <option value="">All Targets</option>
            </select>
            
            <button onclick="refreshData()">🔄 Refresh</button>
            <button onclick="toggleAutoRefresh()">⏱️ Auto Refresh</button>
            <span id="autoRefreshStatus">Off</span>
        </div>

        <div class="grid">
            <div class="panel">
                <h3>📊 Network Metrics</h3>
                <div id="metricsContainer">
                    <div class="loading">Loading metrics...</div>
                </div>
            </div>
            
            <div class="panel">
                <h3>🎯 Network Status</h3>
                <div id="statusContainer">
                    <div class="loading">Loading status...</div>
                </div>
            </div>
        </div>

        <div class="panel">
            <h3>🗺️ Network Path Visualization</h3>
            <div id="network-graph"></div>
        </div>

        <div class="panel">
            <h3>📋 Detailed Hop Information</h3>
            <div id="hopTableContainer">
                <div class="loading">Loading hop data...</div>
            </div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let networkGraph = null;

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            initializeNetworkGraph();
            refreshData();
        });

        function initializeNetworkGraph() {
            const container = document.getElementById('network-graph');
            const data = {
                nodes: new vis.DataSet([]),
                edges: new vis.DataSet([])
            };
            const options = {
                layout: {
                    hierarchical: {
                        direction: 'LR',
                        sortMethod: 'directed',
                        levelSeparation: 150,
                        nodeSpacing: 100
                    }
                },
                physics: false,
                nodes: {
                    shape: 'box',
                    margin: 10,
                    font: { size: 12 }
                },
                edges: {
                    arrows: { to: true },
                    font: { size: 10 },
                    smooth: { type: 'cubicBezier' }
                }
            };
            networkGraph = new vis.Network(container, data, options);
        }

        async function fetchMetrics() {
            try {
                const response = await fetch('http://localhost:8084/metrics');
                const text = await response.text();
                return parsePrometheusMetrics(text);
            } catch (error) {
                console.error('Error fetching metrics:', error);
                return null;
            }
        }

        function parsePrometheusMetrics(text) {
            const lines = text.split('\n');
            const metrics = {
                traceroute_hops: [],
                traceroute_success: [],
                hop_table: []
            };

            lines.forEach(line => {
                if (line.startsWith('synthetic_traceroute_hops{')) {
                    metrics.traceroute_hops.push(parseMetricLine(line));
                } else if (line.startsWith('synthetic_traceroute_success_total{')) {
                    metrics.traceroute_success.push(parseMetricLine(line));
                } else if (line.startsWith('synthetic_traceroute_hop_table{')) {
                    metrics.hop_table.push(parseMetricLine(line));
                }
            });

            return metrics;
        }

        function parseMetricLine(line) {
            const match = line.match(/^([^{]+)\{([^}]+)\}\s+(.+)$/);
            if (!match) return null;

            const [, metricName, labelsStr, value] = match;
            const labels = {};
            
            const labelMatches = labelsStr.matchAll(/(\w+)="([^"]+)"/g);
            for (const labelMatch of labelMatches) {
                labels[labelMatch[1]] = labelMatch[2];
            }

            return {
                metric: metricName,
                labels: labels,
                value: parseFloat(value)
            };
        }

        async function refreshData() {
            const metrics = await fetchMetrics();
            if (!metrics) {
                showError('Failed to fetch metrics data');
                return;
            }

            updateMetrics(metrics);
            updateNetworkGraph(metrics);
            updateHopTable(metrics);
            updateFilters(metrics);
        }

        function updateMetrics(metrics) {
            const container = document.getElementById('metricsContainer');
            const totalHops = metrics.traceroute_hops.reduce((sum, m) => sum + m.value, 0);
            const totalSuccess = metrics.traceroute_success.reduce((sum, m) => sum + m.value, 0);
            const networks = new Set(metrics.hop_table.map(m => m.labels.network)).size;

            container.innerHTML = `
                <div class="metric-card">
                    <div class="metric-value">${totalHops}</div>
                    <div class="metric-label">Total Hops</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${totalSuccess}</div>
                    <div class="metric-label">Successful Traces</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${networks}</div>
                    <div class="metric-label">Networks Monitored</div>
                </div>
            `;
        }

        function updateNetworkGraph(metrics) {
            const nodes = new vis.DataSet();
            const edges = new vis.DataSet();
            
            // Group hops by network and target
            const hopsByPath = {};
            metrics.hop_table.forEach(hop => {
                const pathKey = `${hop.labels.network}-${hop.labels.target}`;
                if (!hopsByPath[pathKey]) {
                    hopsByPath[pathKey] = [];
                }
                hopsByPath[pathKey].push(hop);
            });

            // Create nodes and edges for each path
            Object.entries(hopsByPath).forEach(([pathKey, hops]) => {
                hops.sort((a, b) => parseInt(a.labels.hop_number) - parseInt(b.labels.hop_number));
                
                hops.forEach((hop, index) => {
                    const nodeId = `${pathKey}-${hop.labels.hop_number}`;
                    const rtt = (hop.value * 1000).toFixed(1);
                    
                    nodes.add({
                        id: nodeId,
                        label: `${hop.labels.hop_name || hop.labels.hop_ip}\n${rtt}ms`,
                        title: `Hop ${hop.labels.hop_number}: ${hop.labels.hop_ip}\nRTT: ${rtt}ms\nNetwork: ${hop.labels.network}`,
                        color: getRttColor(hop.value * 1000)
                    });

                    if (index > 0) {
                        const prevNodeId = `${pathKey}-${hops[index-1].labels.hop_number}`;
                        edges.add({
                            from: prevNodeId,
                            to: nodeId,
                            label: `${rtt}ms`
                        });
                    }
                });
            });

            networkGraph.setData({ nodes, edges });
        }

        function getRttColor(rtt) {
            if (rtt < 50) return { background: '#28a745', border: '#1e7e34' };
            if (rtt < 100) return { background: '#ffc107', border: '#e0a800' };
            return { background: '#dc3545', border: '#c82333' };
        }

        function updateHopTable(metrics) {
            const container = document.getElementById('hopTableContainer');
            const selectedNetwork = document.getElementById('networkSelect').value;
            const selectedTarget = document.getElementById('targetSelect').value;

            let filteredHops = metrics.hop_table;
            if (selectedNetwork) {
                filteredHops = filteredHops.filter(h => h.labels.network === selectedNetwork);
            }
            if (selectedTarget) {
                filteredHops = filteredHops.filter(h => h.labels.target === selectedTarget);
            }

            filteredHops.sort((a, b) => {
                const networkCompare = a.labels.network.localeCompare(b.labels.network);
                if (networkCompare !== 0) return networkCompare;
                return parseInt(a.labels.hop_number) - parseInt(b.labels.hop_number);
            });

            const tableHTML = `
                <table class="hop-table">
                    <thead>
                        <tr>
                            <th>Network</th>
                            <th>Hop #</th>
                            <th>IP Address</th>
                            <th>Hostname</th>
                            <th>RTT (ms)</th>
                            <th>Target</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredHops.map(hop => `
                            <tr>
                                <td>${hop.labels.network}</td>
                                <td>${hop.labels.hop_number}</td>
                                <td>${hop.labels.hop_ip}</td>
                                <td>${hop.labels.hop_name || '-'}</td>
                                <td>${(hop.value * 1000).toFixed(1)}</td>
                                <td>${hop.labels.target}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            container.innerHTML = tableHTML;
        }

        function updateFilters(metrics) {
            const networks = new Set(metrics.hop_table.map(m => m.labels.network));
            const targets = new Set(metrics.hop_table.map(m => m.labels.target));

            const networkSelect = document.getElementById('networkSelect');
            const targetSelect = document.getElementById('targetSelect');

            updateSelectOptions(networkSelect, Array.from(networks));
            updateSelectOptions(targetSelect, Array.from(targets));
        }

        function updateSelectOptions(select, options) {
            const currentValue = select.value;
            const defaultOption = select.children[0];
            
            select.innerHTML = '';
            select.appendChild(defaultOption);
            
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option;
                optionElement.textContent = option;
                select.appendChild(optionElement);
            });
            
            if (options.includes(currentValue)) {
                select.value = currentValue;
            }
        }

        function toggleAutoRefresh() {
            const button = event.target;
            const status = document.getElementById('autoRefreshStatus');
            
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                button.textContent = '⏱️ Auto Refresh';
                status.textContent = 'Off';
            } else {
                autoRefreshInterval = setInterval(refreshData, 30000); // 30 seconds
                button.textContent = '⏹️ Stop Auto Refresh';
                status.textContent = 'On (30s)';
            }
        }

        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            document.querySelector('.container').insertBefore(errorDiv, document.querySelector('.controls'));
            
            setTimeout(() => errorDiv.remove(), 5000);
        }

        // Event listeners for filters
        document.getElementById('networkSelect').addEventListener('change', () => {
            refreshData();
        });

        document.getElementById('targetSelect').addEventListener('change', () => {
            refreshData();
        });
    </script>
</body>
</html>
