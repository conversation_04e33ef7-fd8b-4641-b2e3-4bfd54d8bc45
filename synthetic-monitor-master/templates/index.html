<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Synthetic Monitor Master</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .worker-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #f9f9f9;
        }
        .worker-card.up {
            border-left: 4px solid #4CAF50;
        }
        .worker-card.down {
            border-left: 4px solid #f44336;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-up {
            background-color: #4CAF50;
        }
        .status-down {
            background-color: #f44336;
        }
        .config-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .targets-list {
            margin: 10px 0;
        }
        .target-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        .target-item input {
            flex: 1;
            margin-right: 10px;
        }
        .alert {
            padding: 15px;
            margin: 20px 0;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .alert-error {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .nav-bar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 15px 0;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
        }
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .nav-title {
            color: white;
            font-size: 1.5em;
            font-weight: bold;
            margin: 0;
        }
        .nav-links {
            display: flex;
            gap: 20px;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .nav-links a:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-bar">
            <div class="nav-content">
                <h1 class="nav-title">🌐 Synthetic Monitor</h1>
                <div class="nav-links">
                    <a href="/" class="active">📊 Dashboard</a>
                    <a href="/traceroute">🗺️ Traceroute</a>
                    <a href="http://localhost:3000" target="_blank">📈 Grafana</a>
                    <a href="http://localhost:9090" target="_blank">🔍 Prometheus</a>
                </div>
            </div>
        </div>

        <div id="alerts"></div>

        <h2>Worker Status</h2>
        <div id="worker-status" class="status-grid">
            <!-- Worker status will be loaded here -->
        </div>

        <div class="config-section">
            <h2>Configuration</h2>
            <div id="config-form">
                <!-- Configuration form will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        let currentConfig = {};

        function showAlert(message, type = 'success') {
            const alertsDiv = document.getElementById('alerts');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alertsDiv.appendChild(alert);

            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        function loadWorkerStatus() {
            fetch('/api/workers')
                .then(response => response.json())
                .then(data => {
                    const statusDiv = document.getElementById('worker-status');
                    statusDiv.innerHTML = '';

                    Object.entries(data).forEach(([workerId, status]) => {
                        const card = document.createElement('div');
                        card.className = `worker-card ${status.status}`;
                        card.innerHTML = `
                            <h3>Worker: ${workerId.toUpperCase()}</h3>
                            <p>
                                <span class="status-indicator status-${status.status}"></span>
                                Status: ${status.status}
                            </p>
                            ${status.last_seen ? `<p>Last seen: ${new Date(status.last_seen).toLocaleString()}</p>` : ''}
                            ${status.error ? `<p style="color: red;">Error: ${status.error}</p>` : ''}
                        `;
                        statusDiv.appendChild(card);
                    });
                })
                .catch(error => {
                    console.error('Error loading worker status:', error);
                    showAlert('Failed to load worker status', 'error');
                });
        }

        function loadConfig() {
            fetch('/api/config')
                .then(response => response.json())
                .then(config => {
                    currentConfig = config;
                    renderConfigForm();
                })
                .catch(error => {
                    console.error('Error loading config:', error);
                    showAlert('Failed to load configuration', 'error');
                });
        }

        function renderConfigForm() {
            const formDiv = document.getElementById('config-form');

            let html = `
                <h3>Test Configuration</h3>
                <div class="form-group">
                    <label>
                        <input type="checkbox" ${currentConfig.tests?.ping?.enabled ? 'checked' : ''}
                               onchange="updateTestConfig('ping', 'enabled', this.checked)">
                        Enable Ping Tests
                    </label>
                </div>
                <div class="form-group">
                    <label>Ping Interval (seconds):</label>
                    <input type="number" value="${currentConfig.tests?.ping?.interval || 3}"
                           onchange="updateTestConfig('ping', 'interval', parseInt(this.value))">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" ${currentConfig.tests?.traceroute?.enabled ? 'checked' : ''}
                               onchange="updateTestConfig('traceroute', 'enabled', this.checked)">
                        Enable Traceroute Tests
                    </label>
                </div>
                <div class="form-group">
                    <label>Traceroute Interval (seconds):</label>
                    <input type="number" value="${currentConfig.tests?.traceroute?.interval || 300}"
                           onchange="updateTestConfig('traceroute', 'interval', parseInt(this.value))">
                </div>

                <h3>Worker Configuration</h3>
            `;

            Object.entries(currentConfig.workers || {}).forEach(([workerId, workerConfig]) => {
                html += `
                    <div class="worker-config" style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 4px;">
                        <h4>Worker: ${workerId.toUpperCase()}</h4>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" ${workerConfig.enabled ? 'checked' : ''}
                                       onchange="updateWorkerConfig('${workerId}', 'enabled', this.checked)">
                                Enable Worker
                            </label>
                        </div>
                        <div class="form-group">
                            <label>Targets:</label>
                            <div class="targets-list" id="targets-${workerId}">
                                ${(workerConfig.targets || []).map((target, index) => `
                                    <div class="target-item">
                                        <input type="text" value="${target}"
                                               onchange="updateTarget('${workerId}', ${index}, this.value)">
                                        <button class="btn-danger" onclick="removeTarget('${workerId}', ${index})">Remove</button>
                                    </div>
                                `).join('')}
                            </div>
                            <button onclick="addTarget('${workerId}')">Add Target</button>
                        </div>
                    </div>
                `;
            });

            html += '<button onclick="saveConfig()">Save Configuration</button>';
            formDiv.innerHTML = html;
        }

        function updateTestConfig(testType, key, value) {
            if (!currentConfig.tests) currentConfig.tests = {};
            if (!currentConfig.tests[testType]) currentConfig.tests[testType] = {};
            currentConfig.tests[testType][key] = value;
        }

        function updateWorkerConfig(workerId, key, value) {
            if (!currentConfig.workers) currentConfig.workers = {};
            if (!currentConfig.workers[workerId]) currentConfig.workers[workerId] = {};
            currentConfig.workers[workerId][key] = value;
        }

        function updateTarget(workerId, index, value) {
            if (!currentConfig.workers[workerId].targets) {
                currentConfig.workers[workerId].targets = [];
            }
            currentConfig.workers[workerId].targets[index] = value;
        }

        function addTarget(workerId) {
            if (!currentConfig.workers[workerId].targets) {
                currentConfig.workers[workerId].targets = [];
            }
            currentConfig.workers[workerId].targets.push('');
            renderConfigForm();
        }

        function removeTarget(workerId, index) {
            currentConfig.workers[workerId].targets.splice(index, 1);
            renderConfigForm();
        }

        function saveConfig() {
            fetch('/api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(currentConfig)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showAlert('Configuration saved successfully!');
                } else {
                    showAlert(data.message || 'Failed to save configuration', 'error');
                }
            })
            .catch(error => {
                console.error('Error saving config:', error);
                showAlert('Failed to save configuration', 'error');
            });
        }

        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadWorkerStatus();
            loadConfig();

            // Refresh worker status every 30 seconds
            setInterval(loadWorkerStatus, 30000);
        });
    </script>
</body>
</html>