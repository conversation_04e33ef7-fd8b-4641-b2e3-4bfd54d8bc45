#!/usr/bin/env python3

import os
import json
import yaml
import time
import logging
from datetime import datetime
from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_cors import CORS
import requests
from prometheus_client import Counter, Histogram, Gauge, generate_latest, CONTENT_TYPE_LATEST

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Configuration
DATA_DIR = '/app/data'
CONFIG_FILE = os.path.join(DATA_DIR, 'config.json')
PROMETHEUS_URL = os.environ.get('PROMETHEUS_URL', 'http://prometheus:9090')

# Prometheus metrics
worker_status = Gauge('synthetic_worker_status', 'Worker status (1=up, 0=down)', ['worker_id'])
config_changes = Counter('synthetic_config_changes_total', 'Total configuration changes')
api_requests = Counter('synthetic_api_requests_total', 'Total API requests', ['endpoint', 'method'])

# Default configuration
DEFAULT_CONFIG = {
    "workers": {
        "ee": {"enabled": True, "targets": ["8.8.8.8"]},
        "gm": {"enabled": True, "targets": ["8.8.8.8"]},
        "info": {"enabled": True, "targets": ["8.8.8.8"]},
        "orfr": {"enabled": True, "targets": ["8.8.8.8"]},
        "wifi": {"enabled": True, "targets": ["8.8.8.8"]},
        "vfiot": {"enabled": True, "targets": ["8.8.8.8"]}
    },
    "tests": {
        "ping": {
            "enabled": True,
            "interval": 3,
            "timeout": 2
        },
        "traceroute": {
            "enabled": True,
            "interval": 300,
            "timeout": 30
        }
    }
}

def ensure_data_dir():
    """Ensure data directory exists"""
    os.makedirs(DATA_DIR, exist_ok=True)

def load_config():
    """Load configuration from file"""
    ensure_data_dir()
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r') as f:
                return json.load(f)
        else:
            # Create default config
            save_config(DEFAULT_CONFIG)
            return DEFAULT_CONFIG
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        return DEFAULT_CONFIG

def save_config(config):
    """Save configuration to file"""
    ensure_data_dir()
    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=2)
        config_changes.inc()
        logger.info("Configuration saved successfully")
        return True
    except Exception as e:
        logger.error(f"Error saving config: {e}")
        return False

def notify_workers(config):
    """Notify all workers of configuration changes"""
    workers = ['ee', 'gm', 'info', 'orfr', 'wifi', 'vfiot']
    for worker_id in workers:
        try:
            worker_url = f"http://synthetic-worker-{worker_id}:8080"
            response = requests.post(
                f"{worker_url}/config",
                json=config,
                timeout=5
            )
            if response.status_code == 200:
                worker_status.labels(worker_id=worker_id).set(1)
                logger.info(f"Successfully updated worker {worker_id}")
            else:
                worker_status.labels(worker_id=worker_id).set(0)
                logger.warning(f"Failed to update worker {worker_id}: {response.status_code}")
        except Exception as e:
            worker_status.labels(worker_id=worker_id).set(0)
            logger.error(f"Error notifying worker {worker_id}: {e}")

@app.route('/')
def index():
    """Main dashboard"""
    api_requests.labels(endpoint='/', method='GET').inc()
    config = load_config()
    return render_template('index.html', config=config)

@app.route('/traceroute')
def traceroute():
    """Traceroute visualization dashboard"""
    api_requests.labels(endpoint='/traceroute', method='GET').inc()
    return render_template('traceroute.html')

@app.route('/api/config', methods=['GET'])
def get_config():
    """Get current configuration"""
    api_requests.labels(endpoint='/api/config', method='GET').inc()
    config = load_config()
    return jsonify(config)

@app.route('/api/config', methods=['POST'])
def update_config():
    """Update configuration"""
    api_requests.labels(endpoint='/api/config', method='POST').inc()
    try:
        new_config = request.get_json()
        if save_config(new_config):
            notify_workers(new_config)
            return jsonify({"status": "success", "message": "Configuration updated"})
        else:
            return jsonify({"status": "error", "message": "Failed to save configuration"}), 500
    except Exception as e:
        logger.error(f"Error updating config: {e}")
        return jsonify({"status": "error", "message": str(e)}), 400

@app.route('/api/workers', methods=['GET'])
def get_workers():
    """Get worker status"""
    api_requests.labels(endpoint='/api/workers', method='GET').inc()
    workers = ['ee', 'gm', 'info', 'orfr', 'wifi', 'vfiot']
    worker_status_data = {}

    for worker_id in workers:
        try:
            worker_url = f"http://synthetic-worker-{worker_id}:8080"
            response = requests.get(f"{worker_url}/health", timeout=5)
            if response.status_code == 200:
                worker_status_data[worker_id] = {
                    "status": "up",
                    "last_seen": datetime.now().isoformat()
                }
                worker_status.labels(worker_id=worker_id).set(1)
            else:
                worker_status_data[worker_id] = {
                    "status": "down",
                    "last_seen": None
                }
                worker_status.labels(worker_id=worker_id).set(0)
        except Exception as e:
            worker_status_data[worker_id] = {
                "status": "down",
                "last_seen": None,
                "error": str(e)
            }
            worker_status.labels(worker_id=worker_id).set(0)

    return jsonify(worker_status_data)

@app.route('/metrics')
def metrics():
    """Prometheus metrics endpoint"""
    return generate_latest(), 200, {'Content-Type': CONTENT_TYPE_LATEST}

@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "timestamp": datetime.now().isoformat()})

if __name__ == '__main__':
    # Initialize configuration on startup
    config = load_config()
    notify_workers(config)

    # Start the Flask app
    app.run(host='0.0.0.0', port=8080, debug=False)
