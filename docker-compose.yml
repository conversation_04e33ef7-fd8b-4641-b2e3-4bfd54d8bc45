version: '3.8'

services:
  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: synthetic-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - monitoring

  # Tempo for distributed tracing (traceroute visualization)
  tempo:
    image: grafana/tempo:latest
    container_name: synthetic-tempo
    command: [ "-config.file=/etc/tempo.yaml" ]
    user: "0:0"  # Run as root to avoid permission issues
    volumes:
      - ./tempo/tempo.yaml:/etc/tempo.yaml
      - tempo_data:/var/tempo
    ports:
      - "3200:3200"   # Tempo HTTP
      - "4317:4317"   # OTLP gRPC receiver
      - "4318:4318"   # OTLP HTTP receiver
    networks:
      - monitoring

  # Grafana for visualization
  grafana:
    image: grafana/grafana-oss:latest
    container_name: synthetic-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - monitoring
    depends_on:
      - tempo

  # Master container for configuration and coordination
  synthetic-master:
    build:
      context: ./synthetic-monitor-master
      dockerfile: Dockerfile
    container_name: synthetic-master
    ports:
      - "8080:8080"
    volumes:
      - master_data:/app/data
    environment:
      - PROMETHEUS_URL=http://prometheus:9090
    networks:
      - monitoring
    depends_on:
      - prometheus

  # Worker containers for each network
  synthetic-worker-ee:
    build:
      context: ./synthetic-monitor-worker
      dockerfile: Dockerfile
    container_name: synthetic-worker-ee
    environment:
      - WORKER_ID=ee
      - MASTER_URL=http://synthetic-master:8080
      - PROMETHEUS_URL=http://prometheus:9090
      - PING_INTERVAL=3
      - TRACEROUTE_INTERVAL=300
    volumes:
      - worker_ee_data:/app/data
    networks:
      - monitoring
      - ee
    depends_on:
      - synthetic-master

  synthetic-worker-gm:
    build:
      context: ./synthetic-monitor-worker
      dockerfile: Dockerfile
    container_name: synthetic-worker-gm
    environment:
      - WORKER_ID=gm
      - MASTER_URL=http://synthetic-master:8080
      - PROMETHEUS_URL=http://prometheus:9090
      - PING_INTERVAL=3
      - TRACEROUTE_INTERVAL=300
    volumes:
      - worker_gm_data:/app/data
    networks:
      - monitoring
      - gm
    depends_on:
      - synthetic-master

  synthetic-worker-info:
    build:
      context: ./synthetic-monitor-worker
      dockerfile: Dockerfile
    container_name: synthetic-worker-info
    environment:
      - WORKER_ID=info
      - MASTER_URL=http://synthetic-master:8080
      - PROMETHEUS_URL=http://prometheus:9090
      - PING_INTERVAL=3
      - TRACEROUTE_INTERVAL=300
    volumes:
      - worker_info_data:/app/data
    networks:
      - monitoring
      - info
    depends_on:
      - synthetic-master

  synthetic-worker-orfr:
    build:
      context: ./synthetic-monitor-worker
      dockerfile: Dockerfile
    container_name: synthetic-worker-orfr
    environment:
      - WORKER_ID=orfr
      - MASTER_URL=http://synthetic-master:8080
      - PROMETHEUS_URL=http://prometheus:9090
      - PING_INTERVAL=3
      - TRACEROUTE_INTERVAL=300
    volumes:
      - worker_orfr_data:/app/data
    networks:
      - monitoring
      - orfr
    depends_on:
      - synthetic-master

  synthetic-worker-wifi:
    build:
      context: ./synthetic-monitor-worker
      dockerfile: Dockerfile
    container_name: synthetic-worker-wifi
    environment:
      - WORKER_ID=wifi
      - MASTER_URL=http://synthetic-master:8080
      - PROMETHEUS_URL=http://prometheus:9090
      - PING_INTERVAL=3
      - TRACEROUTE_INTERVAL=300
    volumes:
      - worker_wifi_data:/app/data
    networks:
      - monitoring
      - wifi
    cap_add:
      - NET_ADMIN
    depends_on:
      - synthetic-master

  synthetic-worker-vfiot:
    build:
      context: ./synthetic-monitor-worker
      dockerfile: Dockerfile
    container_name: synthetic-worker-vfiot
    environment:
      - WORKER_ID=vfiot
      - MASTER_URL=http://synthetic-master:8080
      - PROMETHEUS_URL=http://prometheus:9090
      - PING_INTERVAL=3
      - TRACEROUTE_INTERVAL=300
    volumes:
      - worker_vfiot_data:/app/data
    networks:
      - monitoring
      - vfiot
    cap_add:
      - NET_ADMIN
    depends_on:
      - synthetic-master

  # Synthetic Monitoring API - Compatible with Grafana Cloud format
  synthetic-monitoring-api:
    build:
      context: .
      dockerfile: Dockerfile.synthetic-api
    container_name: synthetic-monitoring-api
    ports:
      - "8090:8090"
    environment:
      - PROMETHEUS_URL=http://prometheus:9090
    networks:
      - monitoring
    depends_on:
      - prometheus

  # SNMP Exporter for Juniper vSRX RPM metrics
  snmp-exporter:
    image: prom/snmp-exporter:latest
    container_name: snmp-exporter
    ports:
      - "9116:9116"
    volumes:
      - ./snmp.yml:/etc/snmp_exporter/snmp.yml:ro
    command:
      - '--config.file=/etc/snmp_exporter/snmp.yml'
      - '--web.listen-address=:9116'
    networks:
      - monitoring
    restart: unless-stopped

networks:
  monitoring:
    driver: bridge
  # External networks (assuming they already exist)
  ee:
    external: true
  gm:
    external: true
  info:
    external: true
  orfr:
    external: true
  wifi:
    external: true
  vfiot:
    external: true

volumes:
  prometheus_data:
  grafana_data:
  tempo_data:
  master_data:
  worker_ee_data:
  worker_gm_data:
  worker_info_data:
  worker_orfr_data:
  worker_wifi_data:
  worker_vfiot_data:
