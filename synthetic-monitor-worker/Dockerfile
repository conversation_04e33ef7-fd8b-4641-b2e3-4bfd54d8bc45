FROM python:3.11-slim

# Install system dependencies including ping and traceroute
RUN apt-get update && apt-get install -y \
    iputils-ping \
    traceroute \
    curl \
    iproute2 \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .
COPY setup-routing.sh .
RUN chmod +x setup-routing.sh

# Create data directory for persistent storage
RUN mkdir -p /app/data

# Expose port
EXPOSE 8080

# Run the application with routing setup
CMD ["sh", "-c", "./setup-routing.sh && python app.py"]