#!/bin/bash

# Setup correct routing for synthetic monitoring workers
# This script ensures that each worker uses its target network as the default route

echo "Setting up routing for worker: $WORKER_ID"

# Wait for network interfaces to be available
echo "Waiting for network interfaces..."
for i in {1..10}; do
    if ip link show eth1 >/dev/null 2>&1; then
        echo "Network interface eth1 is available"
        break
    fi
    echo "Waiting for eth1... (attempt $i/10)"
    sleep 2
done

# Function to setup routing for a specific network
setup_network_routing() {
    local network_name=$1
    local gateway_ip=$2
    local interface=$3

    echo "Setting up routing for $network_name network via $gateway_ip on $interface"

    # Check if interface exists
    if ! ip link show $interface >/dev/null 2>&1; then
        echo "ERROR: Interface $interface not found!"
        return 1
    fi

    # Remove existing default route
    ip route del default 2>/dev/null || true

    # Add new default route via the target network gateway
    if ip route add default via $gateway_ip dev $interface; then
        echo "Default route set to $gateway_ip via $interface"
        return 0
    else
        echo "ERROR: Failed to set default route!"
        return 1
    fi
}

# Setup routing based on worker ID
case "$WORKER_ID" in
    "wifi")
        setup_network_routing "wifi" "***********" "eth1"
        ;;
    "vfiot")
        setup_network_routing "vfiot" "***********" "eth1"
        ;;
    "ee")
        setup_network_routing "ee" "***********" "eth1"
        ;;
    "gm")
        setup_network_routing "gm" "***********" "eth1"
        ;;
    "info")
        setup_network_routing "info" "***********" "eth1"
        ;;
    "orfr")
        setup_network_routing "orfr" "***********" "eth1"
        ;;
    *)
        echo "Unknown worker ID: $WORKER_ID, using default routing"
        ;;
esac

# Display final routing table
echo "Final routing table:"
ip route show

echo "Routing setup complete for $WORKER_ID"
