#!/usr/bin/env python3

import os
import json
import time
import logging
import threading
import subprocess
import re
from datetime import datetime
from flask import Flask, request, jsonify
import requests
from prometheus_client import Counter, Histogram, Gauge, generate_latest, CONTENT_TYPE_LATEST
from opentelemetry import trace, metrics
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.exporter.prometheus import PrometheusMetricReader
from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
from opentelemetry.sdk.resources import Resource

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Configuration from environment variables
WORKER_ID = os.environ.get('WORKER_ID', 'unknown')
MASTER_URL = os.environ.get('MASTER_URL', 'http://synthetic-master:8080')
PROMETHEUS_URL = os.environ.get('PROMETHEUS_URL', 'http://prometheus:9090')
PING_INTERVAL = int(os.environ.get('PING_INTERVAL', 3))
TRACEROUTE_INTERVAL = int(os.environ.get('TRACEROUTE_INTERVAL', 300))

# Data directory for persistent storage
DATA_DIR = '/app/data'
CONFIG_FILE = os.path.join(DATA_DIR, 'config.json')

# Current configuration
current_config = {
    "workers": {
        WORKER_ID: {"enabled": True, "targets": ["8.8.8.8"]}
    },
    "tests": {
        "ping": {"enabled": True, "interval": PING_INTERVAL, "timeout": 2},
        "traceroute": {"enabled": True, "interval": TRACEROUTE_INTERVAL, "timeout": 30}
    }
}

# OpenTelemetry setup with Tempo
resource = Resource.create({
    "service.name": f"synthetic-worker-{WORKER_ID}",
    "service.version": "1.0.0",
    "deployment.environment": "production",
    "worker.id": WORKER_ID
})

# Set up tracing with Tempo
trace_provider = TracerProvider(resource=resource)
otlp_exporter = OTLPSpanExporter(
    endpoint="http://tempo:4317",
    insecure=True
)
span_processor = BatchSpanProcessor(otlp_exporter)
trace_provider.add_span_processor(span_processor)
trace.set_tracer_provider(trace_provider)
tracer = trace.get_tracer(__name__)

# Prometheus metrics
ping_rtt_histogram = Histogram(
    'synthetic_ping_rtt_seconds',
    'RTT of ICMP ping in seconds',
    ['worker_id', 'target', 'network']
)
ping_success_counter = Counter(
    'synthetic_ping_success_total',
    'Total successful pings',
    ['worker_id', 'target', 'network']
)
ping_failure_counter = Counter(
    'synthetic_ping_failure_total',
    'Total failed pings',
    ['worker_id', 'target', 'network']
)
traceroute_hops_gauge = Gauge(
    'synthetic_traceroute_hops',
    'Number of hops in traceroute',
    ['worker_id', 'target', 'network']
)
traceroute_success_counter = Counter(
    'synthetic_traceroute_success_total',
    'Total successful traceroutes',
    ['worker_id', 'target', 'network']
)
traceroute_failure_counter = Counter(
    'synthetic_traceroute_failure_total',
    'Total failed traceroutes',
    ['worker_id', 'target', 'network']
)
traceroute_hop_rtt_histogram = Histogram(
    'synthetic_traceroute_hop_rtt_seconds',
    'RTT of individual traceroute hops in seconds',
    ['worker_id', 'target', 'network', 'hop_number', 'hop_ip']
)
traceroute_path_info = Gauge(
    'synthetic_traceroute_path_info',
    'Traceroute path information as labels',
    ['worker_id', 'target', 'network', 'hop_number', 'hop_ip', 'hop_name']
)

# Standardized hop metrics for dashboard table (first 4 + last 2 hops)
traceroute_hop_table = Gauge(
    'synthetic_traceroute_hop_table',
    'Standardized traceroute hops for table display (first 4 + last 2)',
    ['worker_id', 'target', 'network', 'hop_position', 'hop_number', 'hop_ip', 'hop_name']
)

# Node graph metrics for network topology visualization
traceroute_nodes = Gauge(
    'synthetic_traceroute_nodes',
    'Network nodes for node graph visualization',
    ['worker_id', 'target', 'network', 'node_id', 'node_name', 'node_type']
)

traceroute_edges = Gauge(
    'synthetic_traceroute_edges',
    'Network edges for node graph visualization',
    ['worker_id', 'target', 'network', 'source_id', 'target_id', 'edge_type']
)
def ensure_data_dir():
    """Ensure data directory exists"""
    os.makedirs(DATA_DIR, exist_ok=True)

def load_config():
    """Load configuration from file"""
    global current_config
    ensure_data_dir()
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r') as f:
                current_config = json.load(f)
                logger.info("Configuration loaded from file")
        else:
            save_config()
    except Exception as e:
        logger.error(f"Error loading config: {e}")

def save_config():
    """Save configuration to file"""
    ensure_data_dir()
    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(current_config, f, indent=2)
        logger.info("Configuration saved to file")
    except Exception as e:
        logger.error(f"Error saving config: {e}")

def ping_target(target):
    """Perform ICMP ping to target"""
    try:
        # Use ping command with count=1 and timeout
        cmd = ['ping', '-c', '1', '-W', '2', target]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)

        if result.returncode == 0:
            # Parse RTT from ping output
            rtt_match = re.search(r'time=(\d+\.?\d*)', result.stdout)
            if rtt_match:
                rtt_ms = float(rtt_match.group(1))
                rtt_seconds = rtt_ms / 1000.0

                # Record metrics
                ping_rtt_histogram.labels(
                    worker_id=WORKER_ID,
                    target=target,
                    network=WORKER_ID
                ).observe(rtt_seconds)
                ping_success_counter.labels(
                    worker_id=WORKER_ID,
                    target=target,
                    network=WORKER_ID
                ).inc()

                logger.debug(f"Ping to {target}: {rtt_ms}ms")
                return True, rtt_seconds
            else:
                logger.warning(f"Could not parse RTT from ping output: {result.stdout}")
                ping_failure_counter.labels(
                    worker_id=WORKER_ID,
                    target=target,
                    network=WORKER_ID
                ).inc()
                return False, None
        else:
            logger.warning(f"Ping to {target} failed: {result.stderr}")
            ping_failure_counter.labels(
                worker_id=WORKER_ID,
                target=target,
                network=WORKER_ID
            ).inc()
            return False, None

    except Exception as e:
        logger.error(f"Error pinging {target}: {e}")
        ping_failure_counter.labels(
            worker_id=WORKER_ID,
            target=target,
            network=WORKER_ID
        ).inc()
        return False, None

def traceroute_target(target):
    """Perform traceroute to target"""
    # Create a trace for the entire traceroute operation
    with tracer.start_as_current_span(
        f"traceroute_to_{target}",
        attributes={
            "network.target": target,
            "worker.id": WORKER_ID,
            "operation.type": "traceroute"
        }
    ) as trace_span:
        try:
            # Use traceroute command with timeout
            cmd = ['traceroute', '-w', '2', '-m', '30', target]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=35)

            if result.returncode == 0:
                # Parse traceroute output for individual hops
                lines = result.stdout.strip().split('\n')[1:]  # Skip first line (header)
                hops = 0
                hop_details = []
                all_hops = []  # Store all parsed hops for standardized selection

                for line in lines:
                    if line.strip() and '* * *' not in line:
                        # Parse hop line with multiple patterns
                        hop_match = None
                        hop_num = None
                        hop_name = None
                        hop_ip = None
                        hop_rtt_ms = None

                        # Pattern 1: " 1  gateway (***********)  1.234 ms  1.567 ms  1.890 ms"
                        hop_match = re.match(r'\s*(\d+)\s+([^\s]+)\s+\(([^)]+)\)\s+([0-9.]+)\s+ms', line)
                        if hop_match:
                            hop_num = int(hop_match.group(1))
                            hop_name = hop_match.group(2)
                            hop_ip = hop_match.group(3)
                            hop_rtt_ms = float(hop_match.group(4))
                        else:
                            # Pattern 2: " 1  ***********  1.234 ms  1.567 ms  1.890 ms"
                            hop_match = re.match(r'\s*(\d+)\s+([0-9.]+)\s+([0-9.]+)\s+ms', line)
                            if hop_match:
                                hop_num = int(hop_match.group(1))
                                hop_ip = hop_match.group(2)
                                hop_name = hop_ip
                                hop_rtt_ms = float(hop_match.group(3))
                            else:
                                # Pattern 3: More flexible pattern for various formats
                                hop_match = re.match(r'\s*(\d+)\s+([^\s]+).*?([0-9.]+)\s+ms', line)
                                if hop_match:
                                    hop_num = int(hop_match.group(1))
                                    hop_name_or_ip = hop_match.group(2)
                                    hop_rtt_ms = float(hop_match.group(3))

                                    # Extract IP if it's in parentheses
                                    ip_match = re.search(r'\(([0-9.]+)\)', line)
                                    if ip_match:
                                        hop_ip = ip_match.group(1)
                                        hop_name = hop_name_or_ip
                                    else:
                                        hop_ip = hop_name_or_ip
                                        hop_name = hop_name_or_ip

                        if hop_num is not None and hop_ip is not None and hop_rtt_ms is not None:
                            hop_rtt_seconds = hop_rtt_ms / 1000.0

                            # Store hop details for later processing
                            hop_detail = {
                                'hop': hop_num,
                                'name': hop_name,
                                'ip': hop_ip,
                                'rtt': hop_rtt_seconds
                            }
                            all_hops.append(hop_detail)
                            hop_details.append(hop_detail)
                            hops += 1

                # Create standardized hop table (first 4 + last 2 hops)
                standardized_hops = []
                if all_hops:
                    # First 4 hops
                    for i, hop in enumerate(all_hops[:4]):
                        standardized_hops.append({
                            'position': f'first_{i+1}',
                            'hop': hop
                        })

                    # Last 2 hops (if we have more than 4 hops total)
                    if len(all_hops) > 4:
                        last_hops = all_hops[-2:]
                        for i, hop in enumerate(last_hops):
                            standardized_hops.append({
                                'position': f'last_{i+1}',
                                'hop': hop
                            })

                    # Record standardized hop metrics for table display
                    for std_hop in standardized_hops:
                        hop = std_hop['hop']
                        traceroute_hop_table.labels(
                            worker_id=WORKER_ID,
                            target=target,
                            network=WORKER_ID,
                            hop_position=std_hop['position'],
                            hop_number=str(hop['hop']),
                            hop_ip=hop['ip'],
                            hop_name=hop['name']
                        ).set(hop['rtt'])

                    # Create sequential node graph metrics for proper hop-by-hop visualization
                    if standardized_hops:

                        # Sort hops by hop number to ensure proper sequence
                        sorted_hops = sorted(standardized_hops, key=lambda x: int(x['hop']['hop']))

                        # Create source node (network gateway)
                        source_node_id = f"{WORKER_ID}_source"
                        traceroute_nodes.labels(
                            worker_id=WORKER_ID,
                            target=target,
                            network=WORKER_ID,
                            node_id=source_node_id,
                            node_name=f"{WORKER_ID.upper()} Network",
                            node_type="source"
                        ).set(0)

                        # Create hop nodes in sequence and connect them serially
                        prev_node_id = source_node_id
                        for i, std_hop in enumerate(sorted_hops):
                            hop = std_hop['hop']
                            current_node_id = f"{WORKER_ID}_hop_{hop['hop']}"

                            # Create current hop node
                            traceroute_nodes.labels(
                                worker_id=WORKER_ID,
                                target=target,
                                network=WORKER_ID,
                                node_id=current_node_id,
                                node_name=f"Hop {hop['hop']}\\n{hop['name']}\\n{hop['ip']}",
                                node_type="hop"
                            ).set(hop['rtt'] * 1000)  # Convert to milliseconds for better display

                            # Create edge from previous hop to current hop (sequential connection)
                            traceroute_edges.labels(
                                worker_id=WORKER_ID,
                                target=target,
                                network=WORKER_ID,
                                source_id=prev_node_id,
                                target_id=current_node_id,
                                edge_type="sequential"
                            ).set(hop['rtt'] * 1000)  # RTT in milliseconds

                            # Update previous node for next iteration
                            prev_node_id = current_node_id

                        # Create final target node
                        target_node_id = f"{WORKER_ID}_target"
                        traceroute_nodes.labels(
                            worker_id=WORKER_ID,
                            target=target,
                            network=WORKER_ID,
                            node_id=target_node_id,
                            node_name="8.8.8.8\\nGoogle DNS\\nTarget",
                            node_type="target"
                        ).set(0)

                        # Create final edge from last hop to target
                        if sorted_hops:
                            last_hop = sorted_hops[-1]['hop']
                            traceroute_edges.labels(
                                worker_id=WORKER_ID,
                                target=target,
                                network=WORKER_ID,
                                source_id=prev_node_id,
                                target_id=target_node_id,
                                edge_type="final"
                            ).set(last_hop['rtt'] * 1000)

                # Record overall metrics
                traceroute_hops_gauge.labels(
                    worker_id=WORKER_ID,
                    target=target,
                    network=WORKER_ID
                ).set(hops)
                traceroute_success_counter.labels(
                    worker_id=WORKER_ID,
                    target=target,
                    network=WORKER_ID
                ).inc()

                # Add trace attributes for the overall traceroute
                trace_span.set_attributes({
                    "traceroute.total_hops": hops,
                    "traceroute.standardized_hops": len(standardized_hops),
                    "traceroute.success": True
                })

                # Create child spans for each hop in the trace
                for hop in hop_details:
                    with tracer.start_as_current_span(
                        f"hop_{hop['hop']}_{hop['ip']}",
                        attributes={
                            "hop.number": hop['hop'],
                            "hop.ip": hop['ip'],
                            "hop.name": hop['name'],
                            "hop.rtt_ms": hop['rtt'] * 1000,
                            "hop.rtt_seconds": hop['rtt']
                        }
                    ):
                        # Span automatically records timing
                        pass

                logger.info(f"Traceroute to {target}: {hops} hops, standardized: {len(standardized_hops)} hops")
                return True, hops
            else:
                trace_span.set_attributes({
                    "traceroute.success": False,
                    "traceroute.error": result.stderr
                })
                logger.warning(f"Traceroute to {target} failed: {result.stderr}")
                traceroute_failure_counter.labels(
                    worker_id=WORKER_ID,
                    target=target,
                    network=WORKER_ID
                ).inc()
                return False, None

        except Exception as e:
            trace_span.set_attributes({
                "traceroute.success": False,
                "traceroute.error": str(e)
            })
            logger.error(f"Error tracerouting {target}: {e}")
            traceroute_failure_counter.labels(
                worker_id=WORKER_ID,
                target=target,
                network=WORKER_ID
            ).inc()
            return False, None

def ping_loop():
    """Main ping loop"""
    while True:
        try:
            if (current_config.get('tests', {}).get('ping', {}).get('enabled', True) and
                current_config.get('workers', {}).get(WORKER_ID, {}).get('enabled', True)):

                targets = current_config.get('workers', {}).get(WORKER_ID, {}).get('targets', ['8.8.8.8'])
                interval = current_config.get('tests', {}).get('ping', {}).get('interval', PING_INTERVAL)

                for target in targets:
                    ping_target(target)

                time.sleep(interval)
            else:
                time.sleep(10)  # Check config every 10 seconds when disabled

        except Exception as e:
            logger.error(f"Error in ping loop: {e}")
            time.sleep(10)

def traceroute_loop():
    """Main traceroute loop"""
    while True:
        try:
            if (current_config.get('tests', {}).get('traceroute', {}).get('enabled', True) and
                current_config.get('workers', {}).get(WORKER_ID, {}).get('enabled', True)):

                targets = current_config.get('workers', {}).get(WORKER_ID, {}).get('targets', ['8.8.8.8'])
                interval = current_config.get('tests', {}).get('traceroute', {}).get('interval', TRACEROUTE_INTERVAL)

                for target in targets:
                    traceroute_target(target)

                time.sleep(interval)
            else:
                time.sleep(60)  # Check config every minute when disabled

        except Exception as e:
            logger.error(f"Error in traceroute loop: {e}")
            time.sleep(60)

@app.route('/config', methods=['POST'])
def update_config():
    """Receive configuration updates from master"""
    try:
        global current_config
        new_config = request.get_json()
        current_config = new_config
        save_config()
        logger.info("Configuration updated from master")
        return jsonify({"status": "success"})
    except Exception as e:
        logger.error(f"Error updating config: {e}")
        return jsonify({"status": "error", "message": str(e)}), 400

@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "worker_id": WORKER_ID,
        "timestamp": datetime.now().isoformat()
    })

@app.route('/metrics')
def metrics():
    """Prometheus metrics endpoint"""
    return generate_latest(), 200, {'Content-Type': CONTENT_TYPE_LATEST}

if __name__ == '__main__':
    # Load configuration
    load_config()

    # Start ping and traceroute threads
    ping_thread = threading.Thread(target=ping_loop, daemon=True)
    traceroute_thread = threading.Thread(target=traceroute_loop, daemon=True)

    ping_thread.start()
    traceroute_thread.start()

    logger.info(f"Worker {WORKER_ID} starting on port 8080")

    # Start Flask app
    app.run(host='0.0.0.0', port=8080, debug=False)
