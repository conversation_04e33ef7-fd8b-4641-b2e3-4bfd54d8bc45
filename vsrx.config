## Last commit: 2025-06-24 07:40:13 UTC by musa
version 23.2R2.21;
system {
    root-authentication {
        encrypted-password "$6$tID//uDO$tp9U1RGr8QPY0Qf4YQBm7fce8lLn/AXPFBvwd2KH9aTyYBl8//a9wXyu5abxvpQyg.DCGysfzbfTDNUGKhV.r."; ## SECRET-DATA
    }
    login {
        user musa {
            uid 2000;
            class super-user;
            authentication {
                encrypted-password "$6$mgrX1ZeH$.Jjz4B2fWs9NRRLCZaRlU/fb4/VlM5C8bXzLSptcr5xGUXVXhNmQ6ZeNA47JqfdY/la9vYaf2JcDozhHyNRPF."; ## SECRET-DATA
            }
        }
    }
    services {
        ssh {
            sftp-server;
        }
        web-management {
            http {
                interface fxp0.0;
            }
            https {
                system-generated-certificate;
                interface fxp0.0;
            }
        }
    }
    name-server {
        *******;
    }
    syslog {
        file interactive-commands {
            interactive-commands any;
        }
        file messages {
            any any;
            authorization info;
        }
    }
    license {
        autoupdate {
            url https://ae1.juniper.net/junos/key_retrieval;
        }
    }
}
services {
    rpm {
        probe 1 {
            test 1 {
                probe-type icmp-ping;
                target address ***********;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance wifi;
            }
            test 2 {
                probe-type icmp-ping;
                target address *******;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance wifi;
            }
            test 3 {
                probe-type icmp-ping;
                target address *************;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance wifi;
                history-size 20;
            }
            test 4 {
                probe-type icmp-ping;
                target address *******;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance wifi;
            }
        }
        probe 2 {
            test 1 {
                probe-type icmp-ping;
                target address ************;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance info;
            }
            test 2 {
                probe-type icmp-ping;
                target address *******;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance info;
            }
            test 3 {
                probe-type icmp-ping;
                target address *************;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance info;
                history-size 20;
            }
            test 4 {
                probe-type icmp-ping;
                target address *******;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance info;
            }
        }
        probe 3 {
            test 1 {
                probe-type icmp-ping;
                target address ************;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance orfr;
            }
            test 2 {
                probe-type icmp-ping;
                target address *******;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance orfr;
            }
            test 3 {
                probe-type icmp-ping;
                target address *************;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance orfr;
                history-size 20;
            }
            test 4 {
                probe-type icmp-ping;
                target address *******;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance orfr;
            }
        }
        probe 4 {
            test 1 {
                probe-type icmp-ping;
                target address ************;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance gm;
            }
            test 2 {
                probe-type icmp-ping;
                target address *******;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance gm;
            }
            test 3 {
                probe-type icmp-ping;
                target address *************;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance gm;
                history-size 20;
            }
            test 4 {
                probe-type icmp-ping;
                target address *******;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance gm;
            }
        }
        probe 5 {
            test 1 {
                probe-type icmp-ping;
                target address ************;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance ee;
            }
            test 2 {
                probe-type icmp-ping;
                target address *******;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance ee;
            }
            test 3 {
                probe-type icmp-ping;
                target address *************;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance ee;
                history-size 20;
            }
            test 4 {
                probe-type icmp-ping;
                target address *******;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance ee;
            }
        }
        probe 6 {
            test 1 {
                probe-type icmp-ping;
                target address ************;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance vf;
            }
            test 2 {
                probe-type icmp-ping;
                target address *******;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance vf;
            }
            test 3 {
                probe-type icmp-ping;
                target address *************;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance vf;
                history-size 20;
            }
            test 4 {
                probe-type icmp-ping;
                target address *******;
                probe-count 3;
                probe-interval 2;
                test-interval 10;
                routing-instance vf;
            }
        }
    }
}
security {
    pki {
        ca-profile ISRG_Root_X1 {
            ca-identity ISRG_Root_X1;
            pre-load;
        }
        ca-profile Lets_Encrypt {
            ca-identity Lets_Encrypt;
            enrollment {
                url https://acme-v02.api.letsencrypt.org/directory;
            }
        }
    }
    screen {
        ids-option untrust-screen {
            icmp {
                ping-death;
            }
            ip {
                source-route-option;
                tear-drop;
            }
            tcp {
                syn-flood {
                    alarm-threshold 1024;
                    attack-threshold 200;
                    source-threshold 1024;
                    destination-threshold 2048;
                    queue-size 2000; ## Warning: 'queue-size' is deprecated
                    timeout 20;
                }
                land;
            }
        }
    }
    policies {
        from-zone trust to-zone trust {
            policy default-permit {
                match {
                    source-address any;
                    destination-address any;
                    application any;
                }
                then {
                    permit;
                }
            }
        }
        from-zone trust to-zone untrust {
            policy default-permit {
                match {
                    source-address any;
                    destination-address any;
                    application any;
                }
                then {
                    permit;
                }
            }
        }
        default-policy {
            permit-all;
        }
        pre-id-default-policy {
            then {
                log {
                    session-close;
                }
            }
        }
    }
    zones {
        security-zone trust {
            tcp-rst;
        }
        security-zone untrust {
            screen untrust-screen;
        }
        security-zone wifi {
            tcp-rst;
            interfaces {
                ge-0/0/0.0 {
                    host-inbound-traffic {
                        system-services {
                            all;
                        }
                        protocols {
                            all;
                        }
                    }
                }
            }
        }
        security-zone info {
            interfaces {
                ge-0/0/1.0 {
                    host-inbound-traffic {
                        system-services {
                            all;
                        }
                        protocols {
                            all;
                        }
                    }
                }
            }
        }
        security-zone orfr {
            interfaces {
                ge-0/0/2.0 {
                    host-inbound-traffic {
                        system-services {
                            all;
                        }
                        protocols {
                            all;
                        }
                    }
                }
            }
        }
        security-zone gm {
            interfaces {
                ge-0/0/3.0 {
                    host-inbound-traffic {
                        system-services {
                            all;
                        }
                        protocols {
                            all;
                        }
                    }
                }
            }
        }
        security-zone ee {
            interfaces {
                ge-0/0/4.0 {
                    host-inbound-traffic {
                        system-services {
                            all;
                        }
                        protocols {
                            all;
                        }
                    }
                }
            }
        }
        security-zone vf {
            interfaces {
                ge-0/0/5.0 {
                    host-inbound-traffic {
                        system-services {
                            all;
                        }
                        protocols {
                            all;
                        }
                    }
                }
            }
        }
    }
}
interfaces {
    ge-0/0/0 {
        unit 0 {
            family inet {
                address ************/24;
            }
        }
    }
    ge-0/0/1 {
        unit 0 {
            family inet {
                address ************/24;
            }
        }
    }
    ge-0/0/2 {
        unit 0 {
            family inet {
                address ************/24;
            }
        }
    }
    ge-0/0/3 {
        unit 0 {
            family inet {
                address ***********3/24;
            }
        }
    }
    ge-0/0/4 {
        unit 0 {
            family inet {
                address ***********3/24;
            }
        }
    }
    ge-0/0/5 {
        unit 0 {
            family inet {
                address ************/24;
            }
        }
    }
    fxp0 {
        unit 0 {
            family inet {
                dhcp;
            }
        }
    }
}
snmp {
    community vsrx_probe {
        authorization read-only;
        clients {
            *************/32;
            *************/32;
            **************/32;
            **************/32;
            ************/32;
            **************/32;
        }
    }
}
routing-instances {
    ee {
        instance-type virtual-router;
        routing-options {
            static {
                route 0.0.0.0/0 next-hop ***********;
            }
        }
        interface ge-0/0/4.0;
    }
    gm {
        instance-type virtual-router;
        routing-options {
            static {
                route 0.0.0.0/0 next-hop ***********;
            }
        }
        interface ge-0/0/3.0;
    }
    info {
        instance-type virtual-router;
        routing-options {
            static {
                route 0.0.0.0/0 next-hop 192.168.5.1;
            }
        }
        interface ge-0/0/1.0;
    }
    orfr {
        instance-type virtual-router;
        routing-options {
            static {
                route 0.0.0.0/0 next-hop 192.168.6.1;
            }
        }
        interface ge-0/0/2.0;
    }
    vf {
        instance-type virtual-router;
        routing-options {
            static {
                route 0.0.0.0/0 next-hop 192.168.9.1;
            }
        }
        interface ge-0/0/5.0;
    }
    wifi {
        instance-type virtual-router;
        routing-options {
            static {
                route 0.0.0.0/0 next-hop 192.168.3.1;
            }
        }
        interface ge-0/0/0.0;
    }
}
routing-options {
    static {
        route 192.168.110.0/24 next-hop 172.16.20.1;
    }
}