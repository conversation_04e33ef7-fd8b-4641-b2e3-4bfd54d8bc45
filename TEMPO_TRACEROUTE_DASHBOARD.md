# Tempo-Based Traceroute Visualization Dashboard

## 🎯 Overview

This dashboard provides comprehensive traceroute visualization using **Tempo as the primary datasource**, leveraging OpenTelemetry traces generated by your synthetic monitoring workers. It combines the power of distributed tracing with network monitoring to give you detailed insights into network paths and performance.

## 📊 Dashboard URL

**Grafana Dashboard**: `http://localhost:3000/d/synthetic-traceroute-tempo/synthetic-monitoring-traceroute-tempo`

## 🏗️ Architecture

### Data Flow
```
Synthetic Workers → OpenTelemetry Traces → Tempo → Grafana Dashboard
                 ↘ Prometheus Metrics → Grafana Dashboard
```

### Components
- **Workers**: Generate traceroute traces with hop-by-hop spans
- **Tempo**: Stores and indexes distributed traces
- **Prometheus**: Stores aggregated metrics
- **Grafana**: Visualizes both trace and metric data

## 📋 Dashboard Panels

### 1. **Network Traceroute Path - Tempo Traces** (Node Graph)
- **Data Source**: Tempo
- **Query**: `{ name =~ "traceroute_to_.*" && resource.worker.id =~ "$worker" }`
- **Visualization**: Interactive node graph showing network paths
- **Features**: 
  - Layered layout algorithm
  - Color-coded by latency
  - Clickable nodes for drill-down

### 2. **Traceroute Hop Latency Over Time** (Time Series)
- **Data Source**: Tempo
- **Query**: `{ name =~ "hop_.*" && resource.worker.id =~ "$worker" && span.hop.number != nil }`
- **Visualization**: Line chart showing hop latency trends
- **Features**:
  - Individual hop performance tracking
  - Time-based analysis
  - Multi-worker comparison

### 3. **Total Hops by Worker** (Bar Gauge)
- **Data Source**: Prometheus
- **Query**: `synthetic_traceroute_hops{worker_id=~"$worker"}`
- **Visualization**: Horizontal bar gauge
- **Features**:
  - Quick overview of network complexity
  - Worker comparison
  - Color-coded by hop count

### 4. **Traceroute Success by Worker** (Pie Chart)
- **Data Source**: Prometheus
- **Query**: `synthetic_traceroute_success_total{worker_id=~"$worker"}`
- **Visualization**: Pie chart showing success distribution
- **Features**:
  - Success rate visualization
  - Worker reliability comparison

### 5. **Recent Traceroute Operations** (Table)
- **Data Source**: Tempo
- **Query**: `{ name =~ "traceroute_to_.*" && resource.worker.id =~ "$worker" && span.traceroute.total_hops != nil }`
- **Visualization**: Sortable table
- **Features**:
  - Recent operation history
  - Duration and hop count details
  - Sortable by timestamp

### 6. **Service Map - Network Topology** (Node Graph)
- **Data Source**: Tempo
- **Query Type**: Service Map
- **Visualization**: Force-directed graph
- **Features**:
  - Overall service topology
  - Service relationships
  - Performance indicators

### 7. **Traceroute Operation Rate by Worker** (Time Series)
- **Data Source**: Tempo
- **Query**: `{ name =~ "traceroute_to_.*" && resource.worker.id =~ "$worker" } | rate() by (resource.worker.id)`
- **Visualization**: Line chart showing operation frequency
- **Features**:
  - Operation rate monitoring
  - Worker activity tracking
  - Trend analysis

## 🔧 Variables

### Worker Filter
- **Name**: `$worker`
- **Type**: Query variable
- **Source**: `label_values(synthetic_traceroute_hops, worker_id)`
- **Multi-select**: Yes
- **Include All**: Yes

### Target Filter
- **Name**: `$target`
- **Type**: Query variable
- **Source**: `label_values(synthetic_traceroute_hops, target)`
- **Multi-select**: Yes
- **Include All**: Yes

## 🚀 Key Features

### Tempo Integration
- **Distributed Tracing**: Each traceroute operation is a trace with hop spans
- **Rich Context**: Traces include worker ID, target, hop details
- **Time Correlation**: Precise timing for each network hop
- **Drill-down**: Click on traces to see detailed span information

### OpenTelemetry Attributes
- `resource.worker.id`: Worker identifier (ee, gm, info, orfr, wifi, vfiot)
- `network.target`: Target IP address (e.g., *******)
- `operation.type`: Operation type (traceroute)
- `traceroute.total_hops`: Total number of hops
- `hop.number`: Individual hop number
- `hop.ip`: Hop IP address
- `hop.rtt_seconds`: Round-trip time in seconds

### Combined Data Sources
- **Tempo**: Real-time trace data with detailed hop information
- **Prometheus**: Aggregated metrics for trends and statistics
- **Unified View**: Both trace-level detail and metric-level aggregation

## 📈 Use Cases

### Network Path Analysis
- Visualize complete network paths from each worker
- Identify routing changes over time
- Compare paths between different networks

### Performance Monitoring
- Track hop-by-hop latency
- Identify network bottlenecks
- Monitor performance trends

### Troubleshooting
- Drill down into specific traceroute operations
- Correlate network issues with timing
- Analyze service dependencies

### Capacity Planning
- Monitor operation rates
- Track success rates by worker
- Identify network complexity trends

## 🔍 TraceQL Queries

The dashboard uses TraceQL (Tempo's query language) for advanced trace filtering:

```traceql
# All traceroute operations
{ name =~ "traceroute_to_.*" }

# Traceroutes from specific worker
{ name =~ "traceroute_to_.*" && resource.worker.id = "vfiot" }

# Individual hops with RTT data
{ name =~ "hop_.*" && span.hop.rtt_seconds > 0.1 }

# Failed traceroutes
{ name =~ "traceroute_to_.*" && span.traceroute.success = false }
```

## 🛠️ Maintenance

### Dashboard Updates
- File: `dashboard-tempo-traceroute.json`
- Import script: `import-traceroute-dashboard.py`
- Auto-provisioning: `grafana/provisioning/dashboards/tempo-traceroute.yml`

### Data Retention
- Tempo traces: Configured in `tempo/tempo.yaml`
- Default retention: 1 hour (configurable)
- Prometheus metrics: Standard retention policies

## 🎉 Benefits

1. **Real-time Visualization**: Live network path monitoring
2. **Detailed Tracing**: Hop-by-hop analysis with precise timing
3. **Multi-worker Support**: Compare paths across all networks
4. **Interactive Exploration**: Drill-down capabilities
5. **Combined Insights**: Trace details + metric aggregations
6. **Scalable Architecture**: Built on proven observability stack

This dashboard transforms your synthetic monitoring data into actionable network insights using the power of distributed tracing and modern observability tools.
