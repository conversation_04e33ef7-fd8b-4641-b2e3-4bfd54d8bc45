{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [], "panels": [{"datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 3, "w": 24, "x": 0, "y": 0}, "id": 5, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "# vSRX RPM Latency Dashboard\n\n**Status**: ✅ SNMP Connected | 🔍 Searching for RPM data at **************\n\n**RPM Tests Found**: 49, 50, 51, 52, 53, 54 (6 active probes detected)\n\n**Expected Mapping**: wifi(49), info(50), orfr(51), gm(52), ee(53), vf(54) → *******\n\n**Current Issue**: RPM summary metrics not available at expected OIDs. Dashboard ready for when data becomes available.\n\n**Troubleshoot**: Run `./check-rpm-status.sh` for detailed diagnostics", "mode": "markdown"}, "pluginVersion": "10.0.0", "title": "Dashboard Status", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "µs"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*wifi.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*info.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*orfr.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*gm.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*ee.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "purple", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*vf.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}]}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 3}, "id": 1, "options": {"legend": {"calcs": ["lastNotNull", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jnxRpmResRtt or jnxRpmResSumAvgRtt", "legendFormat": "Test {{rpm_test_id}} RTT", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "snmp_scrape_pdus_returned{instance=\"**************\"} * 1000", "legendFormat": "SNMP PDUs Found (x1000 for visibility)", "range": true, "refId": "B"}], "title": "vSRX RPM Probe Latency - Individual Samples", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50000}, {"color": "red", "value": 100000}]}, "unit": "µs"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "id": 2, "options": {"displayMode": "lcd", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 16, "minVizWidth": 8, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "valueMode": "color"}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "wifi_FW_rtt or wifi_gw_rtt or wifi_cflare_rtt or wifi_googl_rtt or info_FW_rtt or info_gw_rtt or info_cflare_rtt or info_googl_rtt or orfr_FW_rtt or orfr_gw_rtt or orfr_cflare_rtt or orfr_googl_rtt or gm_FW_rtt or gm_gw_rtt or gm_cflare_rtt or gm_googl_rtt or ee_FW_rtt or ee_gw_rtt or ee_cflare_rtt or ee_googl_rtt or vf_FW_rtt or vf_gw_rtt or vf_cflare_rtt or vf_googl_rtt", "legendFormat": "{{__name__}}", "range": true, "refId": "A"}], "title": "Current Latency by Probe", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 12, "y": 12}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sysUpTime{instance=\"**************\"} / 100", "legendFormat": "vSRX Uptime", "range": true, "refId": "A"}], "title": "vSRX System Uptime", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 12, "y": 16}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "snmp_scrape_duration_seconds{instance=\"**************\"}", "legendFormat": "SNMP Scrape Duration", "range": true, "refId": "A"}], "title": "SNMP Collection Performance", "type": "timeseries"}], "preload": false, "schemaVersion": 41, "tags": ["vsrx", "rpm", "latency", "juniper", "network"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "vSRX RPM Latency Monitoring", "uid": "vsrx-rpm-latency", "version": 1}