{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "synthetic-monitoring"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 13, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"edges": {}, "layoutAlgorithm": "layered", "nodes": {}, "zoomMode": "cooperative"}, "targets": [{"datasource": {"type": "prometheus", "uid": "synthetic-monitoring"}, "instance": "*******", "job": "synthetic_traceroute", "queryType": "traceroute", "refId": "A"}], "title": "Network Traceroute Path - Synthetic Monitoring API", "type": "nodeGraph"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 0, "y": 13}, "id": 2, "interval": "5m", "options": {"displayMode": "lcd", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 16, "minVizWidth": 8, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "valueMode": "color"}, "targets": [{"editorMode": "code", "expr": "synthetic_traceroute_hops{target=\"*******\"}", "legendFormat": "{{worker_id}}", "range": true, "refId": "A"}], "title": "Total Hops by Worker", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 1, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 8, "y": 13}, "id": 3, "interval": "5m", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "synthetic_traceroute_hop_table{target=\"*******\"} * 1000", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{worker_id}} - Hop {{hop_number}}", "refId": "A"}], "title": "Hop RTT Over Time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 16, "y": 13}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "synthetic_traceroute_success_total{target=\"*******\"}", "legendFormat": "{{worker_id}}", "range": true, "refId": "A"}], "title": "Traceroute Success by Worker", "type": "piechart"}], "preload": false, "schemaVersion": 41, "tags": ["synthetic-monitoring", "traceroute", "api"], "templating": {"list": [{"current": {"selected": false, "text": "*******", "value": "*******"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(synthetic_traceroute_hops, target)", "hide": 0, "includeAll": false, "label": "Target", "multi": false, "name": "target", "options": [], "query": {"query": "label_values(synthetic_traceroute_hops, target)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Synthetic Monitoring - Traceroute API", "uid": "synthetic-monitoring-traceroute", "version": 1}