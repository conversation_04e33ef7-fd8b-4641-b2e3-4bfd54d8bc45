{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "tempo", "uid": "tempo"}, "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "color": {"mode": "continuous-GrYlRd"}}, "overrides": []}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"edges": {"mainStatUnit": "ms", "secondaryStatUnit": "", "arrowSize": 10, "colorMode": "field"}, "nodes": {"mainStatUnit": "short", "secondaryStatUnit": "", "arrowSize": 10, "colorMode": "field"}, "layoutAlgorithm": "layered", "zoomMode": "cooperative"}, "targets": [{"datasource": {"type": "tempo", "uid": "tempo"}, "queryType": "traceql", "query": "{ name =~ \"traceroute_to_.*\" && resource.worker.id =~ \"$worker\" }", "refId": "A"}], "title": "Network Traceroute Path - Tempo Traces", "type": "nodeGraph"}, {"datasource": {"type": "tempo", "uid": "tempo"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "tempo", "uid": "tempo"}, "queryType": "traceql", "query": "{ name =~ \"hop_.*\" && resource.worker.id =~ \"$worker\" && span.hop.number != nil }", "refId": "A"}], "title": "Traceroute Hop Latency Over Time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 12}, "id": 3, "options": {"displayMode": "lcd", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 16, "minVizWidth": 8, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "valueMode": "color"}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "synthetic_traceroute_hops{worker_id=~\"$worker\"}", "legendFormat": "{{worker_id}} to {{target}}", "range": true, "refId": "A"}], "title": "Total Hops by Worker", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 12}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "synthetic_traceroute_success_total{worker_id=~\"$worker\"}", "legendFormat": "{{worker_id}}", "range": true, "refId": "A"}], "title": "Traceroute Success by Worker", "type": "piechart"}, {"datasource": {"type": "tempo", "uid": "tempo"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Duration"}, "properties": [{"id": "custom.cellOptions", "value": {"type": "color-background"}}, {"id": "color", "value": {"mode": "continuous-GrYlRd"}}, {"id": "unit", "value": "ms"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}, "id": 5, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Start time"}]}, "targets": [{"datasource": {"type": "tempo", "uid": "tempo"}, "queryType": "traceql", "query": "{ name =~ \"traceroute_to_.*\" && resource.worker.id =~ \"$worker\" && span.traceroute.total_hops != nil }", "refId": "A"}], "title": "Recent Traceroute Operations", "type": "table"}, {"datasource": {"type": "tempo", "uid": "tempo"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "min": 0}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "id": 6, "options": {"edges": {"mainStatUnit": "ms", "secondaryStatUnit": "", "arrowSize": 10, "colorMode": "field"}, "nodes": {"mainStatUnit": "short", "secondaryStatUnit": "", "arrowSize": 10, "colorMode": "field"}, "layoutAlgorithm": "force", "zoomMode": "cooperative"}, "targets": [{"datasource": {"type": "tempo", "uid": "tempo"}, "queryType": "serviceMap", "query": "", "refId": "A"}], "title": "Service Map - Network Topology", "type": "nodeGraph"}, {"datasource": {"type": "tempo", "uid": "tempo"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 28}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "tempo", "uid": "tempo"}, "queryType": "traceql", "query": "{ name =~ \"traceroute_to_.*\" && resource.worker.id =~ \"$worker\" } | rate() by (resource.worker.id)", "refId": "A"}], "title": "Traceroute Operation Rate by Worker", "type": "timeseries"}], "preload": false, "schemaVersion": 41, "tags": ["synthetic-monitoring", "traceroute", "tempo", "network"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(synthetic_traceroute_hops, worker_id)", "hide": 0, "includeAll": true, "label": "Worker", "multi": true, "name": "worker", "options": [], "query": {"query": "label_values(synthetic_traceroute_hops, worker_id)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(synthetic_traceroute_hops, target)", "hide": 0, "includeAll": true, "label": "Target", "multi": true, "name": "target", "options": [], "query": {"query": "label_values(synthetic_traceroute_hops, target)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Synthetic Monitoring - Traceroute (Tempo)", "uid": "synthetic-traceroute-tempo", "version": 1}