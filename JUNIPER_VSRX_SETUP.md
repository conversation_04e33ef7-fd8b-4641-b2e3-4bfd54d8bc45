# Juniper vSRX RPM Integration with Prometheus

## 🎯 Overview

This setup integrates your Juniper vSRX RPM (Real-time Performance Monitoring) data into your existing Prometheus monitoring stack using SNMP exporter. This provides hardware-level network performance metrics to complement your synthetic monitoring workers.

## 🏗️ Architecture

```
Juniper vSRX → SNMP Exporter → Prometheus → Grafana Dashboard
     ↓              ↓              ↓            ↓
  RPM Probes    SNMP Metrics   Time Series   Visualization
```

## 📋 Components Added

### 1. **SNMP Exporter**
- **Container**: `snmp-exporter`
- **Port**: `9116`
- **Config**: `snmp.yml`
- **Purpose**: Converts SNMP data to Prometheus metrics

### 2. **Prometheus Job**
- **Job Name**: `juniper-vsrx-rpm`
- **Target**: vSRX management IP
- **Scrape Interval**: 30 seconds
- **Module**: `juniper_rpm`

### 3. **Grafana Dashboard**
- **File**: `dashboard-juniper-rpm.json`
- **UID**: `juniper-vsrx-rpm`
- **Features**: RTT monitoring, packet loss, probe rates

## 🔧 Setup Steps

### Step 1: Find vSRX Management IP

Your vSRX configuration shows multiple interfaces:
- `************/24` (ge-0/0/1)
- `************/24` (ge-0/0/2)
- `************/24` (ge-0/0/3)
- `************/24` (ge-0/0/4)
- `************/24` (ge-0/0/5)
- `************/24` (ge-0/0/6)
- `fxp0.0` (management - DHCP)

**Find the correct IP:**
```bash
# Test connectivity to possible IPs
./test-vsrx-snmp.sh ************
./test-vsrx-snmp.sh ************
# ... try other IPs

# Or check vSRX console
show interfaces terse | match fxp0
show interfaces fxp0.0
```

### Step 2: Update Prometheus Configuration

Edit `prometheus/prometheus.yml` and update the target IP:

```yaml
- job_name: 'juniper-vsrx-rpm'
  static_configs:
    - targets:
        - YOUR_VSRX_IP_HERE  # Replace with actual IP
```

### Step 3: Restart Services

```bash
# Restart Prometheus to pick up new config
docker compose restart prometheus

# Check SNMP exporter is running
docker compose ps snmp-exporter
```

### Step 4: Verify Data Collection

```bash
# Test SNMP connectivity
./test-vsrx-snmp.sh YOUR_VSRX_IP

# Check Prometheus targets
curl http://localhost:9090/api/v1/targets | jq '.data.activeTargets[] | select(.job=="juniper-vsrx-rpm")'

# Check metrics
curl http://localhost:9090/api/v1/query?query=jnxRpmResSumAvgRtt
```

## 📊 Available Metrics

### RPM Summary Metrics
- `jnxRpmResSumAvgRtt` - Average round-trip time (microseconds)
- `jnxRpmResSumMinRtt` - Minimum round-trip time (microseconds)
- `jnxRpmResSumMaxRtt` - Maximum round-trip time (microseconds)
- `jnxRpmResSumPercentLost` - Packet loss percentage
- `jnxRpmResSumSent` - Total probes sent (counter)
- `jnxRpmResSumReceived` - Total probes received (counter)

### Labels
- `jnxRpmResSumCollection` - RPM collection identifier
- `instance` - vSRX IP address
- `job` - Prometheus job name

## 🗺️ RPM Probe Mapping

Your vSRX has 6 RPM probes configured:

| Probe | Routing Instance | Target | Purpose |
|-------|------------------|--------|---------|
| wifi-probe | wifi | ******* | WiFi network monitoring |
| info-probe | info | ******* | Info network monitoring |
| orfr-probe | orfr | ******* | ORFR network monitoring |
| gm-probe | gm | ******* | GM network monitoring |
| ee-probe | ee | ******* | EE network monitoring |
| vf-probe | vf | ******* | VF network monitoring |

These correspond to your synthetic monitoring workers and provide hardware-level validation of the same network paths.

## 📈 Dashboard Features

### 1. **Round Trip Time Panel**
- Shows min/max/avg RTT for all collections
- Time series visualization
- Microsecond precision

### 2. **Packet Loss Panel**
- Percentage packet loss by collection
- Identifies network reliability issues

### 3. **Current RTT Bar Gauge**
- Real-time RTT comparison
- Color-coded by performance

### 4. **Probe Rate Panel**
- Shows probe frequency
- Sent vs received rates

### 5. **Summary Table**
- Tabular view of all metrics
- Easy comparison across collections

## 🔍 Troubleshooting

### SNMP Connection Issues
```bash
# Test basic SNMP
snmpget -v2c -c vsrx_probe -t 5 YOUR_VSRX_IP *******.*******.0

# Check SNMP exporter logs
docker logs snmp-exporter

# Test SNMP exporter directly
curl "http://localhost:9116/snmp?target=YOUR_VSRX_IP&module=juniper_rpm&auth=public_v2"
```

### No Data in Grafana
1. Check Prometheus targets: `http://localhost:9090/targets`
2. Verify SNMP connectivity with test script
3. Check vSRX SNMP configuration
4. Ensure correct IP address in Prometheus config

### vSRX SNMP Configuration
Verify these settings on your vSRX:
```
set snmp community vsrx_probe authorization read-only
set snmp community vsrx_probe clients 0.0.0.0/0
commit
```

## 🎉 Benefits

### Hardware-Level Validation
- Validates synthetic monitoring results
- Provides router-perspective metrics
- Hardware timestamp accuracy

### Network Troubleshooting
- Correlate software vs hardware metrics
- Identify routing vs application issues
- Precise latency measurements

### Comprehensive Monitoring
- 6 network paths monitored
- Real-time performance data
- Historical trend analysis

## 🔗 Integration with Existing Monitoring

Your setup now provides **dual-perspective monitoring**:

| Perspective | Source | Metrics | Granularity |
|-------------|--------|---------|-------------|
| **Software** | Synthetic Workers | Ping, Traceroute, HTTP | Application-level |
| **Hardware** | vSRX RPM | RTT, Packet Loss | Network-level |

This combination gives you complete visibility into network performance from both application and infrastructure perspectives.

## 📁 Files Created

- `snmp.yml` - SNMP exporter configuration
- `test-vsrx-snmp.sh` - Connectivity test script
- `dashboard-juniper-rpm.json` - Grafana dashboard
- `JUNIPER_VSRX_SETUP.md` - This documentation

## 🚀 Next Steps

1. **Find correct vSRX IP** using the test script
2. **Update Prometheus config** with the correct IP
3. **Import Grafana dashboard** for visualization
4. **Compare metrics** between synthetic workers and vSRX RPM
5. **Set up alerting** based on RPM thresholds
