#!/usr/bin/env python3
"""
Script to import the traceroute visualization dashboard into Grafana
"""

import json
import requests
import os
import sys

def import_dashboard(dashboard_file="dashboard-tempo-traceroute.json"):
    """Import the traceroute dashboard into Grafana"""

    # Grafana configuration
    grafana_url = "http://localhost:3000"  # Adjust if different
    grafana_user = "admin"
    grafana_password = "admin"  # Adjust if different

    # Load the dashboard JSON
    try:
        with open(dashboard_file, 'r') as f:
            dashboard_json = json.load(f)
    except FileNotFoundError:
        print(f"Error: {dashboard_file} not found")
        return False
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in dashboard file: {e}")
        return False
    
    # Prepare the dashboard for import
    dashboard_payload = {
        "dashboard": dashboard_json,
        "overwrite": True,
        "message": "Imported traceroute visualization dashboard"
    }
    
    # Import the dashboard
    try:
        response = requests.post(
            f"{grafana_url}/api/dashboards/db",
            json=dashboard_payload,
            auth=(grafana_user, grafana_password),
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            dashboard_url = f"{grafana_url}/d/{result['uid']}/{result['slug']}"
            print(f"✅ Dashboard imported successfully!")
            print(f"📊 Dashboard URL: {dashboard_url}")
            print(f"🆔 Dashboard UID: {result['uid']}")
            return True
        else:
            print(f"❌ Failed to import dashboard: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Could not connect to Grafana at {grafana_url}")
        print("Make sure Grafana is running and accessible")
        return False
    except Exception as e:
        print(f"❌ Error importing dashboard: {e}")
        return False

def main():
    """Main function"""
    import sys

    # Check if a specific dashboard file is provided
    if len(sys.argv) > 1 and sys.argv[1].endswith('.json'):
        dashboard_file = sys.argv[1]
        print(f"🚀 Importing Dashboard: {dashboard_file}")
        print("=" * 50)

        success = import_dashboard(dashboard_file)

        if success:
            print(f"\n✅ Dashboard {dashboard_file} imported successfully!")
        else:
            print(f"\n❌ Dashboard {dashboard_file} import failed!")
            sys.exit(1)
        return

    dashboard_type = sys.argv[1] if len(sys.argv) > 1 else "tempo"

    if dashboard_type == "synthetic":
        print("🚀 Importing Synthetic Monitoring API Dashboard...")
        print("=" * 50)

        success = import_dashboard("dashboard-synthetic-monitoring.json")

        if success:
            print("\n✅ Synthetic Monitoring API Dashboard imported successfully!")
            print("\n📋 Dashboard Features:")
            print("  • Node Graph - Compatible with Grafana Cloud Synthetic Monitoring")
            print("  • API Format - Uses synthetic monitoring datasource format")
            print("  • Hop Visualization - Network path from API data")
            print("  • RTT Analysis - Time series of hop response times")
            print("  • Success Metrics - Worker performance tracking")
            print("\n🔧 Variables:")
            print("  • Target filter - Select specific targets to analyze")
            print("\n💡 Data Sources:")
            print("  • Primary: Synthetic Monitoring API (port 8090)")
            print("  • Secondary: Prometheus (metrics)")
        else:
            print("\n❌ Dashboard import failed!")
            sys.exit(1)
    else:
        print("🚀 Importing Tempo-based Traceroute Dashboard...")
        print("=" * 50)

        success = import_dashboard("dashboard-tempo-traceroute.json")

        if success:
            print("\n✅ Tempo Traceroute Dashboard imported successfully!")
            print("\n📋 Dashboard Features:")
            print("  • Node Graph - Visual network path from Tempo traces")
            print("  • Hop Latency - Time series from trace spans")
            print("  • Service Map - Network topology visualization")
            print("  • Recent Operations - Table of recent traceroute traces")
            print("  • Operation Rate - Traceroute frequency by worker")
            print("  • Success Metrics - Combined Prometheus + Tempo data")
            print("\n🔧 Variables:")
            print("  • Worker filter - Select specific workers to view")
            print("  • Target filter - Select specific targets to analyze")
            print("\n💡 Data Sources:")
            print("  • Primary: Tempo (traces and spans)")
            print("  • Secondary: Prometheus (metrics)")
        else:
            print("\n❌ Dashboard import failed!")
            sys.exit(1)

if __name__ == "__main__":
    main()
