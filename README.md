# Synthetic Monitoring System

A comprehensive synthetic monitoring solution with Grafana OSS, Prometheus, and a custom ICMP probe service with OpenTelemetry integration.

## Features

- 🔍 **ICMP Ping Monitoring**: Configurable probes for monitoring network connectivity and RTT
- 📊 **Modern Web Interface**: Beautiful, responsive web UI for managing probes
- 📈 **Prometheus Integration**: Metrics exported to Prometheus for storage and alerting
- 🔄 **OpenTelemetry Support**: Metrics sent via OpenTelemetry for modern observability
- 📱 **Grafana Dashboards**: Pre-configured Grafana for visualization
- 🐳 **Containerized**: Full Docker Compose setup for easy deployment

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Synthetic       │───▶│ OpenTelemetry    │───▶│ Prometheus      │
│ Monitor         │    │ Collector        │    │                 │
│ (ICMP Probes)   │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                                                 │
         │                                                 ▼
         ▼                                      ┌─────────────────┐
┌─────────────────┐                            │ Grafana OSS     │
│ Web Interface   │                            │ (Dashboards)    │
│ (Port 8082)     │                            │ (Port 3001)     │
└─────────────────┘                            └─────────────────┘
```

## Quick Start

### Prerequisites

- Docker and Docker Compose installed
- At least 4GB of available RAM
- Network access to target IPs you want to monitor

### 1. Deploy the Stack

```bash
# Clone or download the project
cd syn_copilot

# Start all services
docker compose up -d

# Check status
docker compose ps
```

### 2. Access the Interfaces

- **Synthetic Monitor Web UI**: http://localhost:8082
- **Grafana Dashboard**: http://localhost:3001 (admin/admin)
- **Prometheus UI**: http://localhost:9091
- **OpenTelemetry Collector**: http://localhost:4320

### 3. Configure Your First Probe

1. Open http://localhost:8082 in your browser
2. Fill in the "Add New Probe" form:
   - **Name**: e.g., "Google DNS"
   - **Target IP**: e.g., "*******"
   - **Interval**: e.g., "60 seconds"
3. Click "Add Probe"
4. The probe will start immediately and begin collecting metrics

### 4. View Metrics

- **Real-time metrics**: Visit http://localhost:8082/metrics
- **Grafana visualization**: Visit http://localhost:3001
- **Prometheus queries**: Visit http://localhost:9091

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `OTEL_ENDPOINT` | `http://otel-collector:4318` | OpenTelemetry collector endpoint |
| `PROMETHEUS_ENDPOINT` | `http://prometheus:9090` | Prometheus server endpoint |

### Probe Configuration

Probes are configured through the web interface and stored in `/app/config/probes.json`. Each probe includes:

- **Name**: Human-readable identifier
- **Target IP**: IPv4 address to ping
- **Interval**: Ping frequency in seconds (30s - 30m)
- **Enabled**: Active/inactive status

## Metrics

### Prometheus Metrics

| Metric | Type | Description |
|--------|------|-------------|
| `ping_rtt_seconds` | Histogram | RTT of ICMP ping in seconds |
| `ping_success_total` | Counter | Total successful pings |
| `ping_failure_total` | Counter | Total failed pings |
| `ping_packet_loss_percent` | Gauge | Packet loss percentage |

### OpenTelemetry Metrics

All Prometheus metrics are also exported via OpenTelemetry with additional labels:
- `target`: Target IP address
- `probe_name`: User-defined probe name

## API Endpoints

### Synthetic Monitor Service

- `GET /` - Web interface
- `POST /add_probe` - Add new probe
- `POST /toggle_probe/<ip>` - Enable/disable probe
- `POST /delete_probe/<ip>` - Delete probe
- `GET /metrics` - Prometheus metrics
- `GET /api/probes` - JSON list of probes
- `GET /health` - Health check

### Example API Usage

```bash
# Get all probes
curl http://localhost:8082/api/probes

# Get Prometheus metrics
curl http://localhost:8082/metrics

# Health check
curl http://localhost:8082/health
```

## Monitoring Examples

### Sample Prometheus Queries

```promql
# Average RTT by target
avg by (target) (ping_rtt_seconds)

# Success rate over 5 minutes
rate(ping_success_total[5m]) / (rate(ping_success_total[5m]) + rate(ping_failure_total[5m]))

# Packet loss percentage
ping_packet_loss_percent

# Failed pings in the last hour
increase(ping_failure_total[1h])
```

### Grafana Dashboard Setup

1. Login to Grafana (http://localhost:3001) with admin/admin
2. Prometheus datasource is pre-configured
3. Create new dashboard with panels for:
   - RTT time series graph
   - Success rate gauge
   - Packet loss heatmap
   - Failed pings table

## Troubleshooting

### Common Issues

1. **Permission denied for ping**
   ```bash
   # Check if container has proper capabilities
   docker logs synthetic-monitor
   ```

2. **Metrics not appearing in Prometheus**
   ```bash
   # Verify scrape targets
   curl http://localhost:9091/api/v1/targets
   ```

3. **OpenTelemetry not receiving metrics**
   ```bash
   # Check collector logs
   docker logs otel-collector
   ```

### Log Access

```bash
# View all logs
docker compose logs

# Specific service logs
docker compose logs synthetic-monitor
docker compose logs prometheus
docker compose logs grafana
docker compose logs otel-collector
```

### Restart Services

```bash
# Restart all services
docker compose restart

# Restart specific service
docker compose restart synthetic-monitor
```

## Development

### Local Development

```bash
# Install Python dependencies
cd synthetic-monitor
pip install -r requirements.txt

# Run locally (ensure other services are running)
python app.py
```

### Custom Modifications

- **Add new probe types**: Extend `app.py` with additional probe functions
- **Custom metrics**: Add new Prometheus/OpenTelemetry metrics
- **UI enhancements**: Modify `templates/index.html`
- **Alerting**: Configure Prometheus alerting rules

## Security Considerations

- Change default Grafana password in production
- Use proper network segmentation
- Consider adding authentication to the web interface
- Monitor resource usage and set appropriate limits

## Performance

### Resource Requirements

- **Memory**: ~2GB total for all services
- **CPU**: Minimal (probe intervals determine load)
- **Storage**: ~1GB for metrics retention (configurable)
- **Network**: Depends on probe frequency and targets

### Scaling

- Add more synthetic-monitor replicas for high availability
- Use external Prometheus/Grafana for production
- Consider using Kubernetes for orchestration

## License

This project is provided as-is for educational and monitoring purposes.

---

# NEW: Multi-Worker Synthetic Monitoring System

## Updated Architecture

The system has been redesigned with a distributed architecture:

### Components
1. **Master Container** - Configuration management with web interface (port 8080)
2. **6 Worker Containers** - Network-specific probes (ee, gm, info, orfr, wifi, vf)
3. **Grafana OSS** - Visualization (port 3000)
4. **Prometheus** - Metrics collection (port 9090)

### Key Features
- **ICMP Ping Tests** - Every 3 seconds
- **Traceroute Tests** - Every 5 minutes
- **Persistent Configuration** - Survives restarts
- **Real-time Web Interface** - Easy management
- **OpenTelemetry Metrics** - Modern observability

### Quick Start (New System)

```bash
# Run the setup script
./setup-default-tests.sh

# Or manually start
docker-compose up -d
```

### Access Points
- **Master Web UI**: http://localhost:8080
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090

### Default Configuration
- All 6 workers test *******
- Ping every 3 seconds
- Traceroute every 5 minutes
- Pre-configured Grafana dashboard

The new system provides better scalability and comprehensive monitoring across all network segments.
