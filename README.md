# Multi-Worker Synthetic Monitoring System

A comprehensive distributed synthetic monitoring solution with Grafana OSS, Prometheus, SNMP monitoring, and network-specific worker containers for ICMP ping and traceroute testing.

## Features

- 🔍 **Distributed ICMP Ping Monitoring**: 6 network-specific worker containers for comprehensive coverage
- 🛣️ **Traceroute Analysis**: Network path analysis every 5 minutes
- 📊 **Master Web Interface**: Centralized configuration and management interface
- 📈 **Prometheus Integration**: Metrics collection and storage with alerting capabilities
- 📱 **Grafana Dashboards**: Pre-configured visualization and monitoring dashboards
- 🌐 **SNMP Monitoring**: Juniper vSRX RPM metrics collection
- 🔌 **Synthetic Monitoring API**: Grafana Cloud compatible API endpoint
- 🐳 **Containerized**: Full Docker Compose setup for easy deployment

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Master          │───▶│ 6 Worker        │───▶│ Prometheus      │
│ Container       │    │ Containers      │    │ (Port 9090)     │
│ (Port 8080)     │    │ (ee,gm,info,    │    │                 │
└─────────────────┘    │ orfr,wifi,vfiot)│    │                 │
         │              └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       │                       ▼
┌─────────────────┐              │            ┌─────────────────┐
│ Synthetic API   │              │            │ Grafana OSS     │
│ (Port 8090)     │              │            │ (Port 3000)     │
└─────────────────┘              │            └─────────────────┘
                                 │                       │
                                 ▼                       │
                      ┌─────────────────┐               │
                      │ SNMP Exporter   │◀──────────────┘
                      │ (Port 9116)     │
                      └─────────────────┘
```

## Quick Start

### Prerequisites

- Docker and Docker Compose installed
- At least 6GB of available RAM (for all containers)
- Network access to target IPs you want to monitor
- External Docker networks: ee, gm, info, orfr, wifi, vfiot

### 1. Deploy the Stack

```bash
# Navigate to project directory
cd syn

# Start all services
docker compose up -d

# Check status
docker compose ps
```

### 2. Access the Interfaces

- **Master Web UI**: http://localhost:8080
- **Synthetic Monitoring API**: http://localhost:8090
- **Grafana Dashboard**: http://localhost:3000 (admin/admin)
- **Prometheus UI**: http://localhost:9090
- **SNMP Exporter**: http://localhost:9116

### 3. Configure Your First Probe

1. Open http://localhost:8080 in your browser
2. Use the master interface to configure probes for all workers
3. Each worker automatically tests configured targets:
   - **Ping frequency**: Every 3 seconds
   - **Traceroute frequency**: Every 5 minutes
4. Default configuration: All workers test *******

### 4. View Metrics

- **Master interface**: Visit http://localhost:8080
- **Grafana visualization**: Visit http://localhost:3000
- **Prometheus queries**: Visit http://localhost:9090
- **API endpoint**: Visit http://localhost:8090

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `WORKER_ID` | `ee/gm/info/orfr/wifi/vfiot` | Worker container identifier |
| `MASTER_URL` | `http://synthetic-master:8080` | Master container endpoint |
| `PROMETHEUS_URL` | `http://prometheus:9090` | Prometheus server endpoint |
| `PING_INTERVAL` | `3` | Ping frequency in seconds |
| `TRACEROUTE_INTERVAL` | `300` | Traceroute frequency in seconds |

### Worker Configuration

Each worker container is configured for specific network segments:

- **ee**: Enterprise Edge network monitoring
- **gm**: General Management network monitoring
- **info**: Information network monitoring
- **orfr**: Operational/Facilities network monitoring
- **wifi**: Wireless network monitoring
- **vfiot**: VF IoT network monitoring

Configuration is managed through the master web interface at http://localhost:8080

## Metrics

### Prometheus Metrics

| Metric | Type | Description |
|--------|------|-------------|
| `ping_rtt_seconds` | Histogram | RTT of ICMP ping in seconds |
| `ping_success_total` | Counter | Total successful pings |
| `ping_failure_total` | Counter | Total failed pings |
| `ping_packet_loss_percent` | Gauge | Packet loss percentage |
| `traceroute_hops` | Gauge | Number of hops in traceroute |
| `traceroute_rtt_seconds` | Histogram | RTT for each hop in traceroute |

### Metric Labels

All metrics include labels for detailed analysis:
- `target`: Target IP address
- `worker_id`: Worker container identifier (ee, gm, info, orfr, wifi, vfiot)
- `probe_name`: User-defined probe name
- `hop`: Hop number (for traceroute metrics)

## API Endpoints

### Master Container (Port 8080)

- `GET /` - Master web interface
- `POST /add_probe` - Add new probe configuration
- `POST /toggle_probe/<ip>` - Enable/disable probe
- `POST /delete_probe/<ip>` - Delete probe configuration
- `GET /api/probes` - JSON list of all probes
- `GET /health` - Health check

### Synthetic Monitoring API (Port 8090)

- `GET /` - API status and information
- `GET /api/v1/checks` - List all synthetic checks (Grafana Cloud compatible)
- `POST /api/v1/checks` - Create new synthetic check
- `GET /metrics` - Prometheus metrics endpoint
- `GET /health` - Health check

### Example API Usage

```bash
# Get all probes from master
curl http://localhost:8080/api/probes

# Get Grafana Cloud compatible checks
curl http://localhost:8090/api/v1/checks

# Get Prometheus metrics
curl http://localhost:8090/metrics

# Health checks
curl http://localhost:8080/health
curl http://localhost:8090/health
```

## Monitoring Examples

### Sample Prometheus Queries

```promql
# Average RTT by target and worker
avg by (target, worker_id) (ping_rtt_seconds)

# Success rate over 5 minutes by worker
rate(ping_success_total[5m]) / (rate(ping_success_total[5m]) + rate(ping_failure_total[5m]))

# Packet loss percentage by network segment
ping_packet_loss_percent

# Failed pings in the last hour by worker
increase(ping_failure_total[1h]) by (worker_id)

# Traceroute hop count by target
traceroute_hops by (target, worker_id)

# Average traceroute RTT by hop
avg by (target, hop) (traceroute_rtt_seconds)
```

### Grafana Dashboard Setup

1. Login to Grafana (http://localhost:3000) with admin/admin
2. Prometheus datasource is pre-configured
3. Pre-built dashboards available:
   - **Synthetic Monitoring Dashboard**: Overview of all workers and targets
   - **Tempo Traceroute Dashboard**: Network path analysis and visualization
   - **vSRX Network Latency**: Juniper device RPM metrics
4. Create custom dashboards with panels for:
   - RTT time series by worker and target
   - Success rate gauge by network segment
   - Packet loss heatmap across all workers
   - Traceroute path visualization
   - Failed pings table with worker details

## Troubleshooting

### Common Issues

1. **Permission denied for ping**
   ```bash
   # Check if container has proper capabilities
   docker logs synthetic-monitor
   ```

2. **Metrics not appearing in Prometheus**
   ```bash
   # Verify scrape targets
   curl http://localhost:9091/api/v1/targets
   ```

3. **OpenTelemetry not receiving metrics**
   ```bash
   # Check collector logs
   docker logs otel-collector
   ```

### Log Access

```bash
# View all logs
docker compose logs

# Specific service logs
docker compose logs synthetic-master
docker compose logs synthetic-worker-ee
docker compose logs synthetic-worker-gm
docker compose logs synthetic-monitoring-api
docker compose logs prometheus
docker compose logs grafana
docker compose logs snmp-exporter
```

### Restart Services

```bash
# Restart all services
docker compose restart

# Restart specific services
docker compose restart synthetic-master
docker compose restart synthetic-worker-ee
docker compose restart synthetic-monitoring-api
```

## Development

### Local Development

```bash
# Master container development
cd synthetic-monitor-master
pip install -r requirements.txt
python app.py

# Worker container development
cd synthetic-monitor-worker
pip install -r requirements.txt
python app.py
```

### Custom Modifications

- **Add new worker types**: Create additional worker containers for new network segments
- **Custom probe types**: Extend worker `app.py` with additional probe functions
- **Custom metrics**: Add new Prometheus metrics in workers
- **UI enhancements**: Modify master `templates/index.html`
- **Alerting**: Configure Prometheus alerting rules
- **SNMP monitoring**: Extend `snmp.yml` for additional device types

## Security Considerations

- Change default Grafana password in production
- Use proper network segmentation
- Consider adding authentication to the web interface
- Monitor resource usage and set appropriate limits

## Performance

### Resource Requirements

- **Memory**: ~6GB total for all services (1 master + 6 workers + monitoring stack)
- **CPU**: Moderate (6 workers + frequent ping/traceroute operations)
- **Storage**: ~2GB for metrics retention (configurable)
- **Network**: High frequency testing (ping every 3s, traceroute every 5min per worker)

### Scaling

- Add more worker containers for additional network segments
- Scale workers horizontally for high availability
- Use external Prometheus/Grafana for production environments
- Consider using Kubernetes for orchestration and auto-scaling

## License

This project is provided as-is for educational and monitoring purposes.

## Additional Components

### SNMP Exporter (Port 9116)
- **Purpose**: Collects metrics from Juniper vSRX devices
- **Configuration**: `snmp.yml` contains device-specific SNMP settings
- **Metrics**: RPM (Real-time Performance Monitoring) latency data
- **Integration**: Metrics automatically scraped by Prometheus

### Setup Scripts
- **setup-default-tests.sh**: Configures default monitoring targets for all workers
- **start.sh**: Convenience script to start all services
- **stop.sh**: Convenience script to stop all services

### Dashboard Files
- **dashboard-synthetic-monitoring.json**: Main monitoring dashboard
- **dashboard-tempo-traceroute.json**: Traceroute visualization dashboard
- **dashboard-vsrx-*.json**: Juniper vSRX specific dashboards

---

## Current System Status

✅ **Running Services:**
- Master Container (http://localhost:8080)
- 6 Worker Containers (ee, gm, info, orfr, wifi, vfiot)
- Grafana OSS (http://localhost:3000)
- Prometheus (http://localhost:9090)
- Synthetic Monitoring API (http://localhost:8090)
- SNMP Exporter (http://localhost:9116)

🔧 **System Configuration:**
- Ping tests every 3 seconds per worker
- Traceroute tests every 5 minutes per worker
- Default target: ******* (Google DNS)
- Persistent configuration storage
- Pre-configured Grafana dashboards
