#!/bin/bash

# Synthetic Monitoring System Startup Script
set -e

echo "🚀 Starting Synthetic Monitoring System..."
echo "======================================"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker compose is available
if ! docker compose version &> /dev/null; then
    echo "❌ docker compose is not available. Please install Docker Compose plugin first."
    exit 1
fi

# Stop any existing containers
echo "🧹 Stopping existing containers..."
docker compose down 2>/dev/null || true

# Build and start all services
echo "🔨 Building and starting all services..."
docker compose up --build -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check service status
echo "📊 Checking service status..."
docker compose ps

echo ""
echo "✅ Synthetic Monitoring System is starting up!"
echo ""
echo "🌐 Access Points:"
echo "  • Synthetic Monitor Web UI: http://localhost:8082"
echo "  • Grafana Dashboard:        http://localhost:3001 (admin/admin)"
echo "  • Prometheus UI:            http://localhost:9091"
echo "  • Health Check:             http://localhost:8082/health"
echo ""
echo "📈 Metrics Endpoints:"
echo "  • Prometheus Metrics:       http://localhost:8082/metrics"
echo "  • OpenTelemetry Collector:  http://localhost:4320"
echo ""
echo "🔧 Management:"
echo "  • View logs:    docker compose logs -f"
echo "  • Stop system:  docker compose down"
echo "  • Restart:      docker compose restart"
echo ""

# Test if services are responding
echo "🧪 Testing service connectivity..."

# Test synthetic monitor
if curl -s http://localhost:8082/health > /dev/null; then
    echo "  ✅ Synthetic Monitor: Ready"
else
    echo "  ⏳ Synthetic Monitor: Starting up..."
fi

# Test Prometheus
if curl -s http://localhost:9091/-/ready > /dev/null; then
    echo "  ✅ Prometheus: Ready"
else
    echo "  ⏳ Prometheus: Starting up..."
fi

# Test Grafana
if curl -s http://localhost:3001/api/health > /dev/null; then
    echo "  ✅ Grafana: Ready"
else
    echo "  ⏳ Grafana: Starting up..."
fi

echo ""
echo "🎯 Next Steps:"
echo "  1. Open http://localhost:8082 to configure your first probe"
echo "  2. Add a probe (e.g., Google DNS *******)"
echo "  3. View metrics in Grafana at http://localhost:3001"
echo ""
echo "📚 For more information, see README.md"
