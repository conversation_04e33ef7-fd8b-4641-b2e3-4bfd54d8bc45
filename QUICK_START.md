# 🚀 Synthetic Monitoring System - Quick Start Guide

## Prerequisites Check ✅

Before starting, ensure you have:
- Docker Desktop installed and running
- At least 4GB of available RAM
- Ports 3000, 8080, 9090, 4317, 4318 available

## Starting the System

1. **Start Docker Desktop** (if not already running)

2. **Navigate to the project directory:**
   ```bash
   cd /Users/<USER>/Downloads/syn_copilot
   ```

3. **Start the system:**
   ```bash
   ./start.sh
   ```
   
   Or manually with Docker Compose:
   ```bash
   docker-compose up -d --build
   ```

## Access Points 🌐

Once started, you can access:

- **🔧 Synthetic Monitor Web UI**: http://localhost:8080
- **📊 Grafana Dashboard**: http://localhost:3000 (login: admin/admin)
- **🎯 Prometheus UI**: http://localhost:9090
- **❤️ Health Check**: http://localhost:8080/health

## Quick Test 🧪

1. Open http://localhost:8080
2. Add a probe:
   - Name: "Google DNS"
   - Target IP: "*******" 
   - Interval: "60 seconds"
3. Click "Add Probe"
4. View metrics in Grafana: http://localhost:3000

## Stopping the System 🛑

```bash
./stop.sh
```

Or:
```bash
docker-compose down
```

## Troubleshooting 🔧

If you encounter issues:

1. **Check Docker status:**
   ```bash
   docker info
   ```

2. **View logs:**
   ```bash
   docker-compose logs -f
   ```

3. **Restart services:**
   ```bash
   docker-compose restart
   ```

4. **Clean restart:**
   ```bash
   docker-compose down -v
   ./start.sh
   ```

## What's Included 📦

✅ **Grafana OSS** - Beautiful dashboards and visualization
✅ **Prometheus** - Metrics storage and querying  
✅ **OpenTelemetry Collector** - Modern metrics pipeline
✅ **Synthetic Monitor** - ICMP ping probes with web interface
✅ **Pre-configured Dashboards** - Ready-to-use monitoring views
✅ **Beautiful Web UI** - Easy probe configuration and management

Enjoy monitoring! 🎉
