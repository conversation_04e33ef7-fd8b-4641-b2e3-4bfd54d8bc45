{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [], "panels": [{"datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 3, "w": 24, "x": 0, "y": 0}, "id": 7, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "# vSRX RPM Network Latency Dashboard - All Targets\n\n**Status**: ✅ **16/24 metrics active** | 🔍 Real-time hardware latency from vSRX RPM probes\n\n**Networks**: wifi, info, orfr, gm, ee, vf | **Targets**: F<PERSON> (Firewall), gw (Gateway), cflare (Cloudflare), googl (Google DNS)\n\n**Data Source**: Juniper vSRX RPM probes via SNMP | **Update Frequency**: 5 seconds", "mode": "markdown"}, "pluginVersion": "10.0.0", "title": "Dashboard Overview", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Latency to Firewall/Gateway targets across all networks", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "µs"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*wifi.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*info.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*orfr.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*gm.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*ee.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "purple", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*vf.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 3}, "id": 1, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "wifi_FW_rtt or info_FW_rtt or orfr_FW_rtt or gm_FW_rtt or ee_FW_rtt or vf_FW_rtt", "interval": "", "legendFormat": "{{__name__}}", "refId": "A"}], "title": "Firewall Latency - All Networks", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Latency to Gateway targets across all networks", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "µs"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*wifi.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*info.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*orfr.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*gm.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*ee.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "purple", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*vf.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 3}, "id": 2, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "wifi_gw_rtt or info_gw_rtt or orfr_gw_rtt or gm_gw_rtt or ee_gw_rtt or vf_gw_rtt", "interval": "", "legendFormat": "{{__name__}}", "refId": "A"}], "title": "Gateway Latency - All Networks", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Latency to Cloudflare DNS across all networks", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "µs"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*wifi.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*info.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*orfr.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*gm.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*ee.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "purple", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*vf.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 11}, "id": 3, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "wifi_cflare_rtt or info_cflare_rtt or orfr_cflare_rtt or gm_cflare_rtt or ee_cflare_rtt or vf_cflare_rtt", "interval": "", "legendFormat": "{{__name__}}", "refId": "A"}], "title": "Cloudflare DNS Latency - All Networks", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Latency to Google DNS across all networks (when available)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "µs"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*wifi.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*info.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*orfr.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*gm.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*ee.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "purple", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*vf.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 11}, "id": 4, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "wifi_googl_rtt or info_googl_rtt or orfr_googl_rtt or gm_googl_rtt or ee_googl_rtt or vf_googl_rtt", "interval": "", "legendFormat": "{{__name__}}", "refId": "A"}], "title": "Google DNS Latency - All Networks", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Current latency values for all targets and networks", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 500000, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100000}, {"color": "red", "value": 200000}]}, "unit": "µs"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 19}, "id": 5, "options": {"displayMode": "basic", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true}, "pluginVersion": "10.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "wifi_FW_rtt or wifi_gw_rtt or wifi_cflare_rtt or wifi_googl_rtt or info_FW_rtt or info_gw_rtt or info_cflare_rtt or info_googl_rtt or orfr_FW_rtt or orfr_gw_rtt or orfr_cflare_rtt or orfr_googl_rtt or gm_FW_rtt or gm_gw_rtt or gm_cflare_rtt or gm_googl_rtt or ee_FW_rtt or ee_gw_rtt or ee_cflare_rtt or ee_googl_rtt or vf_FW_rtt or vf_gw_rtt or vf_cflare_rtt or vf_googl_rtt", "interval": "", "legendFormat": "{{__name__}}", "refId": "A"}], "title": "Current Latency - All Targets & Networks", "type": "bargauge"}], "refresh": "5s", "schemaVersion": 41, "style": "dark", "tags": ["vsrx", "rpm", "latency", "network", "all-targets", "juniper"], "templating": {"list": []}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "vSRX Network Latency - All Targets Dashboard", "uid": "vsrx-all-targets", "version": 1}