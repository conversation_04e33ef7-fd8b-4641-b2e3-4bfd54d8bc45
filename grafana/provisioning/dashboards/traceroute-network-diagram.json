{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Network topology diagram showing traceroute paths from each network to *******", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 16, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"edges": {"mainStatUnit": "ms", "secondaryStatUnit": "", "showMainStat": true, "showSecondaryStat": false}, "nodes": {"mainStatUnit": "short", "secondaryStatUnit": "", "showMainStat": false, "showSecondaryStat": false}}, "pluginVersion": "10.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "synthetic_traceroute_hop_table{target=\"*******\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Network Traceroute Topology", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "instance": true, "job": true, "target": true, "worker_id": true}, "indexByName": {}, "renameByName": {"Value": "rtt_seconds", "hop_ip": "hop_ip", "hop_name": "hop_name", "hop_number": "hop_number", "hop_position": "hop_position", "network": "network"}}}, {"id": "calculateField", "options": {"alias": "id", "binary": {"left": "network", "operator": "+", "reducer": "sum", "right": "_"}, "mode": "binary"}}, {"id": "calculateField", "options": {"alias": "id", "binary": {"left": "id", "operator": "+", "reducer": "sum", "right": "hop_ip"}, "mode": "binary"}}, {"id": "calculateField", "options": {"alias": "title", "binary": {"left": "hop_name", "operator": "+", "reducer": "sum", "right": "\\n"}, "mode": "binary"}}, {"id": "calculateField", "options": {"alias": "title", "binary": {"left": "title", "operator": "+", "reducer": "sum", "right": "hop_ip"}, "mode": "binary"}}, {"id": "calculateField", "options": {"alias": "mainStat", "binary": {"left": "rtt_seconds", "operator": "*", "reducer": "sum", "right": "1000"}, "mode": "binary"}}, {"id": "calculateField", "options": {"alias": "source", "binary": {"left": "network", "operator": "+", "reducer": "sum", "right": "_source"}, "mode": "binary"}}, {"id": "calculateField", "options": {"alias": "target", "binary": {"left": "id", "operator": "+", "reducer": "sum", "right": ""}, "mode": "binary"}}], "type": "nodeGraph"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Simplified network paths showing key hops for each network", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 16}, "id": 2, "options": {"displayMode": "table", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "text": {}}, "pluginVersion": "10.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "synthetic_traceroute_hop_table{target=\"*******\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Network Path Visualization", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "instance": true, "job": true, "target": true, "worker_id": true}, "indexByName": {}, "renameByName": {"Value": "RTT (s)", "hop_ip": "IP Address", "hop_name": "Hostname", "hop_number": "Hop", "hop_position": "Position", "network": "Network"}}}, {"id": "groupBy", "options": {"fields": {"Hop": {"aggregations": ["lastNotNull"], "operation": "aggregate"}, "Hostname": {"aggregations": ["lastNotNull"], "operation": "aggregate"}, "IP Address": {"aggregations": ["lastNotNull"], "operation": "aggregate"}, "Network": {"aggregations": [], "operation": "groupby"}, "Position": {"aggregations": ["lastNotNull"], "operation": "aggregate"}, "RTT (s)": {"aggregations": ["lastNotNull"], "operation": "aggregate"}}}}], "type": "stat"}], "refresh": "30s", "schemaVersion": 36, "style": "dark", "tags": ["traceroute", "network", "topology", "diagram"], "templating": {"list": []}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Network Traceroute Topology", "uid": "traceroute-topology", "version": 1, "weekStart": ""}