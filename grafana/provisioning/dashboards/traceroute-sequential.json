{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Sequential hop-by-hop traceroute visualization showing the correct network topology", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.05}, {"color": "red", "value": 0.1}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 20, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"displayMode": "basic", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "text": {"titleSize": 12, "valueSize": 10}}, "pluginVersion": "10.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "synthetic_traceroute_hop_table{target=\"*******\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Sequential Traceroute Topology - Hop by <PERSON>", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "instance": true, "job": true, "target": true, "worker_id": true}, "indexByName": {"Value": 5, "hop_ip": 2, "hop_name": 3, "hop_number": 1, "hop_position": 4, "network": 0}, "renameByName": {"Value": "RTT (seconds)", "hop_ip": "IP Address", "hop_name": "Hostname", "hop_number": "Hop #", "hop_position": "Position", "network": "Network"}}}, {"id": "calculateField", "options": {"alias": "Hop Label", "binary": {"left": "Network", "operator": "+", "reducer": "sum", "right": " → Hop "}, "mode": "binary"}}, {"id": "calculateField", "options": {"alias": "Hop Label", "binary": {"left": "Hop Label", "operator": "+", "reducer": "sum", "right": "Hop #"}, "mode": "binary"}}, {"id": "calculateField", "options": {"alias": "Hop Label", "binary": {"left": "Hop Label", "operator": "+", "reducer": "sum", "right": ": "}, "mode": "binary"}}, {"id": "calculateField", "options": {"alias": "Hop Label", "binary": {"left": "Hop Label", "operator": "+", "reducer": "sum", "right": "IP Address"}, "mode": "binary"}}], "type": "stat"}], "refresh": "30s", "schemaVersion": 36, "style": "dark", "tags": ["traceroute", "sequential", "topology", "hops"], "templating": {"list": []}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Sequential Traceroute Topology", "uid": "traceroute-sequential", "version": 1, "weekStart": ""}