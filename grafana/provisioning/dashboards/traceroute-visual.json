{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Visual representation of sequential traceroute paths", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"content": "<div style=\"font-family: monospace; font-size: 14px; line-height: 1.6; padding: 20px; background: #1f1f1f; color: #ffffff; border-radius: 8px;\">\n  <h3 style=\"color: #58a6ff; margin-bottom: 20px;\">🌐 Sequential Traceroute Topology</h3>\n  \n  <div style=\"margin-bottom: 25px;\">\n    <h4 style=\"color: #f85149; margin-bottom: 10px;\">📡 VFiot Network Path (17 hops total):</h4>\n    <div style=\"background: #0d1117; padding: 15px; border-left: 4px solid #58a6ff; margin-left: 10px;\">\n      <span style=\"color: #7ee787;\">Source</span> \n      <span style=\"color: #ffa657;\">→</span> \n      <span style=\"color: #58a6ff;\">Hop 1: ***********</span> \n      <span style=\"color: #ffa657;\">→</span> \n      <span style=\"color: #58a6ff;\">Hop 3: 192.168.44.193</span> \n      <span style=\"color: #ffa657;\">→</span> \n      <span style=\"color: #58a6ff;\">Hop 4: 192.168.44.194</span> \n      <span style=\"color: #ffa657;\">→</span> \n      <span style=\"color: #58a6ff;\">Hop 5: 172.30.1.142</span> \n      <span style=\"color: #ffa657;\">→</span> \n      <span style=\"color: #8b949e;\">... 11 hops ...</span> \n      <span style=\"color: #ffa657;\">→</span> \n      <span style=\"color: #58a6ff;\">Hop 16: Google Edge</span> \n      <span style=\"color: #ffa657;\">→</span> \n      <span style=\"color: #f85149;\">Hop 17: *******</span>\n    </div>\n  </div>\n  \n  <div style=\"margin-bottom: 25px;\">\n    <h4 style=\"color: #f85149; margin-bottom: 10px;\">📶 WiFi Network Path (11 hops total):</h4>\n    <div style=\"background: #0d1117; padding: 15px; border-left: 4px solid #58a6ff; margin-left: 10px;\">\n      <span style=\"color: #7ee787;\">Source</span> \n      <span style=\"color: #ffa657;\">→</span> \n      <span style=\"color: #58a6ff;\">Hop 1: 192.168.3.1</span> \n      <span style=\"color: #ffa657;\">→</span> \n      <span style=\"color: #58a6ff;\">Hop 2: 192.168.65.41</span> \n      <span style=\"color: #ffa657;\">→</span> \n      <span style=\"color: #58a6ff;\">Hop 3: 192.168.65.46</span> \n      <span style=\"color: #ffa657;\">→</span> \n      <span style=\"color: #58a6ff;\">Hop 4: 172.30.3.78</span> \n      <span style=\"color: #ffa657;\">→</span> \n      <span style=\"color: #8b949e;\">... 5 hops ...</span> \n      <span style=\"color: #ffa657;\">→</span> \n      <span style=\"color: #58a6ff;\">Hop 10: Google Edge</span> \n      <span style=\"color: #ffa657;\">→</span> \n      <span style=\"color: #f85149;\">Hop 11: *******</span>\n    </div>\n  </div>\n  \n  <div style=\"text-align: center; margin-top: 20px; padding: 10px; background: #21262d; border-radius: 6px;\">\n    <span style=\"color: #7ee787;\">🎯 Both paths converge at Google DNS (*******)</span>\n  </div>\n  \n  <div style=\"margin-top: 20px; font-size: 12px; color: #8b949e;\">\n    <strong>Key:</strong> \n    <span style=\"color: #7ee787;\">■ Source Networks</span> | \n    <span style=\"color: #58a6ff;\">■ Intermediate Hops</span> | \n    <span style=\"color: #f85149;\">■ Target Destination</span> | \n    <span style=\"color: #ffa657;\">→ Sequential Flow</span>\n  </div>\n</div>", "mode": "html"}, "pluginVersion": "10.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "synthetic_traceroute_hop_table{target=\"*******\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Sequential Traceroute Topology Visualization", "type": "text"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Live traceroute hop data showing sequential network paths", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.05}, {"color": "red", "value": 0.1}]}, "unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Network"}, "properties": [{"id": "custom.width", "value": 100}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Hop #"}, "properties": [{"id": "custom.width", "value": 80}]}]}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 8}, "id": 2, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "Network"}, {"desc": false, "displayName": "Hop #"}]}, "pluginVersion": "10.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "synthetic_traceroute_hop_table{target=\"*******\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Live Hop Data - Sequential Order", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "instance": true, "job": true, "target": true, "worker_id": true}, "indexByName": {"Value": 5, "hop_ip": 2, "hop_name": 3, "hop_number": 1, "hop_position": 4, "network": 0}, "renameByName": {"Value": "RTT (seconds)", "hop_ip": "IP Address", "hop_name": "Hostname", "hop_number": "Hop #", "hop_position": "Position", "network": "Network"}}}], "type": "table"}], "refresh": "30s", "schemaVersion": 36, "style": "dark", "tags": ["traceroute", "visual", "topology", "sequential"], "templating": {"list": []}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Traceroute Visual Topology", "uid": "traceroute-visual", "version": 1, "weekStart": ""}