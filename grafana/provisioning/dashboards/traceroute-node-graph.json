{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Network topology showing traceroute paths as connected nodes", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 20, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"edges": {"mainStatUnit": "ms", "secondaryStatUnit": "", "showMainStat": true, "showSecondaryStat": false}, "nodes": {"mainStatUnit": "short", "secondaryStatUnit": "", "showMainStat": true, "showSecondaryStat": false}}, "pluginVersion": "10.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "synthetic_traceroute_hop_table{target=\"*******\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Traceroute Network Graph", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "instance": true, "job": true, "target": true, "worker_id": true}, "indexByName": {}, "renameByName": {"Value": "mainStat"}}}, {"id": "calculateField", "options": {"alias": "id", "binary": {"left": "network", "operator": "+", "reducer": "sum", "right": "_"}, "mode": "binary"}}, {"id": "calculateField", "options": {"alias": "id", "binary": {"left": "id", "operator": "+", "reducer": "sum", "right": "hop_number"}, "mode": "binary"}}, {"id": "calculateField", "options": {"alias": "title", "binary": {"left": "hop_name", "operator": "+", "reducer": "sum", "right": "\\n"}, "mode": "binary"}}, {"id": "calculateField", "options": {"alias": "title", "binary": {"left": "title", "operator": "+", "reducer": "sum", "right": "hop_ip"}, "mode": "binary"}}, {"id": "calculateField", "options": {"alias": "source", "binary": {"left": "network", "operator": "+", "reducer": "sum", "right": "_"}, "mode": "binary"}}, {"id": "calculateField", "options": {"alias": "prev_hop_num", "binary": {"left": "hop_number", "operator": "-", "reducer": "sum", "right": "1"}, "mode": "binary"}}, {"id": "calculateField", "options": {"alias": "source", "binary": {"left": "source", "operator": "+", "reducer": "sum", "right": "prev_hop_num"}, "mode": "binary"}}, {"id": "calculateField", "options": {"alias": "target", "binary": {"left": "network", "operator": "+", "reducer": "sum", "right": "_"}, "mode": "binary"}}, {"id": "calculateField", "options": {"alias": "target", "binary": {"left": "target", "operator": "+", "reducer": "sum", "right": "hop_number"}, "mode": "binary"}}], "type": "nodeGraph"}], "refresh": "30s", "schemaVersion": 36, "style": "dark", "tags": ["traceroute", "network", "topology", "nodes"], "templating": {"list": []}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Traceroute Node Graph", "uid": "traceroute-nodes", "version": 1, "weekStart": ""}