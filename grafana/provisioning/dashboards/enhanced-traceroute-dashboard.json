{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Network topology showing traceroute paths with individual hop details", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 14, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"edges": {"mainStatUnit": "s", "secondaryStatUnit": "short"}, "nodes": {"mainStatUnit": "s", "secondaryStatUnit": "short"}, "layoutAlgorithm": "layered", "zoomMode": "cooperative"}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "synthetic_traceroute_path_info{target=\"*******\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Enhanced Traceroute Network Topology", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "instance": true, "job": true, "target": true}, "indexByName": {}, "renameByName": {"Value": "mainStat", "hop_ip": "id", "hop_name": "title", "worker_id": "source", "hop_number": "sortBy"}}}, {"id": "calculateField", "options": {"alias": "arc__*******", "binary": {"left": "mainStat", "operator": "*", "reducer": "sum", "right": "1"}, "mode": "binary", "reduce": {"reducer": "sum"}}}], "type": "nodeGraph"}], "refresh": "30s", "schemaVersion": 36, "style": "dark", "tags": ["traceroute", "network", "topology", "enhanced"], "templating": {"list": []}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Enhanced Traceroute Dashboard", "uid": "enhanced-traceroute", "version": 1, "weekStart": ""}