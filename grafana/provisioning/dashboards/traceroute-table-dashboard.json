{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Standardized traceroute table showing first 4 hops and last 2 hops for each network", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.05}, {"color": "red", "value": 0.1}]}, "unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Network"}, "properties": [{"id": "custom.width", "value": 80}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Position"}, "properties": [{"id": "custom.width", "value": 100}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Hop #"}, "properties": [{"id": "custom.width", "value": 60}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "IP Address"}, "properties": [{"id": "custom.width", "value": 140}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Hostname"}, "properties": [{"id": "custom.width", "value": 200}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "RTT (s)"}, "properties": [{"id": "custom.width", "value": 80}]}]}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "Network"}]}, "pluginVersion": "10.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "synthetic_traceroute_hop_table{target=\"*******\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Traceroute Hop Table (First 4 + Last 2 Hops)", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "instance": true, "job": true, "target": true}, "indexByName": {"network": 0, "hop_position": 1, "hop_number": 2, "hop_ip": 3, "hop_name": 4, "Value": 5}, "renameByName": {"Value": "RTT (s)", "hop_ip": "IP Address", "hop_name": "Hostname", "hop_number": "Hop #", "hop_position": "Position", "network": "Network"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Total number of hops for each network path to *******", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 12}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "synthetic_traceroute_hops{target=\"*******\"}", "interval": "", "legendFormat": "{{network}} network", "refId": "A"}], "title": "Total Traceroute Hops Over Time", "type": "timeseries"}], "refresh": "30s", "schemaVersion": 36, "style": "dark", "tags": ["traceroute", "network", "hops", "table"], "templating": {"list": []}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Traceroute Hop Analysis", "uid": "traceroute-table", "version": 1, "weekStart": ""}