{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Visual representation of network traceroute paths", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"displayMode": "table", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "text": {}}, "pluginVersion": "10.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "synthetic_traceroute_hop_table{target=\"*******\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Network Traceroute Flow", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "instance": true, "job": true, "target": true, "worker_id": true}, "indexByName": {}, "renameByName": {"Value": "RTT (seconds)", "hop_ip": "IP Address", "hop_name": "Hostname", "hop_number": "Hop #", "hop_position": "Position", "network": "Network"}}}, {"id": "groupBy", "options": {"fields": {"Hop #": {"aggregations": ["lastNotNull"], "operation": "aggregate"}, "Hostname": {"aggregations": ["lastNotNull"], "operation": "aggregate"}, "IP Address": {"aggregations": ["lastNotNull"], "operation": "aggregate"}, "Network": {"aggregations": [], "operation": "groupby"}, "Position": {"aggregations": ["lastNotNull"], "operation": "aggregate"}, "RTT (seconds)": {"aggregations": ["lastNotNull"], "operation": "aggregate"}}}}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Detailed traceroute hop information in table format", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.05}, {"color": "red", "value": 0.1}]}, "unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Network"}, "properties": [{"id": "custom.width", "value": 100}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Position"}, "properties": [{"id": "custom.width", "value": 120}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Hop #"}, "properties": [{"id": "custom.width", "value": 80}]}]}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 12}, "id": 2, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "Network"}]}, "pluginVersion": "10.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "synthetic_traceroute_hop_table{target=\"*******\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Traceroute Hop Details", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "instance": true, "job": true, "target": true, "worker_id": true}, "indexByName": {"Value": 5, "hop_ip": 2, "hop_name": 3, "hop_number": 1, "hop_position": 4, "network": 0}, "renameByName": {"Value": "RTT (seconds)", "hop_ip": "IP Address", "hop_name": "Hostname", "hop_number": "Hop #", "hop_position": "Position", "network": "Network"}}}], "type": "table"}], "refresh": "30s", "schemaVersion": 36, "style": "dark", "tags": ["traceroute", "network", "flow", "visualization"], "templating": {"list": []}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Traceroute Flow Diagram", "uid": "traceroute-flow", "version": 1, "weekStart": ""}