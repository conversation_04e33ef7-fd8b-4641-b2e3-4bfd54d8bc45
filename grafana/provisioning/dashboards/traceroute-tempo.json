{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "tempo", "uid": "tempo"}, "description": "Traceroute traces showing sequential hop-by-hop network paths as distributed traces", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"edges": {}, "nodes": {}}, "pluginVersion": "10.0.0", "targets": [{"datasource": {"type": "tempo", "uid": "tempo"}, "query": "{ service.name=\"synthetic-worker-vfiot\" }", "queryType": "", "refId": "A"}, {"datasource": {"type": "tempo", "uid": "tempo"}, "query": "{ service.name=\"synthetic-worker-wifi\" }", "queryType": "", "refId": "B"}], "title": "Traceroute Network Topology (Tempo Traces)", "type": "nodeGraph"}, {"datasource": {"type": "tempo", "uid": "tempo"}, "description": "Search and view individual traceroute traces", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 12}, "id": 2, "options": {"query": "{ operation.type=\"traceroute\" }", "tags": ["network.target", "worker.id", "traceroute.total_hops"]}, "pluginVersion": "10.0.0", "targets": [{"datasource": {"type": "tempo", "uid": "tempo"}, "query": "{ operation.type=\"traceroute\" }", "queryType": "", "refId": "A"}], "title": "Traceroute Trace Search", "type": "traces"}], "refresh": "30s", "schemaVersion": 36, "style": "dark", "tags": ["traceroute", "tempo", "tracing", "network"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Traceroute Tempo Traces", "uid": "traceroute-tempo", "version": 1, "weekStart": ""}