{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Real-time latency to ******* from all six network probes", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.05}, {"color": "red", "value": 0.1}]}, "unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "wifi"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "vfiot"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "ee"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "gm"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "info"}, "properties": [{"id": "color", "value": {"fixedColor": "purple", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "orfr"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "rate(synthetic_ping_rtt_seconds_sum{target=\"*******\"}[1m]) / rate(synthetic_ping_rtt_seconds_count{target=\"*******\"}[1m])", "interval": "", "legendFormat": "{{worker_id}}", "refId": "A"}], "title": "Latency to ******* - All Network Probes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Current latency values for each probe", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 0.2, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.05}, {"color": "red", "value": 0.1}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "id": 2, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "rate(synthetic_ping_rtt_seconds_sum{target=\"*******\"}[1m]) / rate(synthetic_ping_rtt_seconds_count{target=\"*******\"}[1m])", "interval": "", "legendFormat": "{{worker_id}}", "refId": "A"}], "title": "Current Latency by Probe", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Statistics table showing min, max, mean, and current latency for each probe", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.05}, {"color": "red", "value": 0.1}]}, "unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Probe"}, "properties": [{"id": "custom.width", "value": 100}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "id": 3, "options": {"showHeader": true}, "pluginVersion": "9.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "rate(synthetic_ping_rtt_seconds_sum{target=\"*******\"}[1m]) / rate(synthetic_ping_rtt_seconds_count{target=\"*******\"}[1m])", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Latency Statistics Table", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "network": true, "target": true, "__name__": true, "instance": true, "job": true}, "indexByName": {}, "renameByName": {"Value": "Current Latency (s)", "worker_id": "Probe"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Latency distribution histogram for all probes", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 18}, "id": 4, "options": {"bucketOffset": 0, "legend": {"displayMode": "list", "placement": "bottom"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "increase(synthetic_ping_rtt_seconds_bucket{target=\"*******\"}[5m])", "interval": "", "legendFormat": "{{worker_id}} - {{le}}s", "refId": "A"}], "title": "Latency Distribution (5min)", "type": "histogram"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Success rate percentage for ping tests to *******", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 18}, "id": 5, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "rate(synthetic_ping_success_total{target=\"*******\"}[5m]) / rate(synthetic_ping_rtt_seconds_count{target=\"*******\"}[5m]) * 100", "interval": "", "legendFormat": "{{worker_id}}", "refId": "A"}], "title": "Ping Success Rate", "type": "gauge"}], "refresh": "5s", "schemaVersion": 36, "style": "dark", "tags": ["latency", "*******", "network", "probes"], "templating": {"list": []}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Network Latency Dashboard - *******", "uid": "latency-8888", "version": 1, "weekStart": ""}