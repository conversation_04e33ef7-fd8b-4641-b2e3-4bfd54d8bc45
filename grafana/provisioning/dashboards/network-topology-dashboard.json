{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Network topology showing traceroute paths from each probe to *******", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"edges": {"mainStatUnit": "short", "secondaryStatUnit": "short"}, "nodes": {"mainStatUnit": "short", "secondaryStatUnit": "short"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "synthetic_traceroute_hops{target=\"*******\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Traceroute Hop Count by Network", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "instance": true, "job": true, "network": true, "target": true}, "indexByName": {}, "renameByName": {"Value": "Hop Count", "worker_id": "Network Probe"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Hop count comparison across all network probes", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 8, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "id": 2, "options": {"legend": {"calcs": ["lastNotNull", "mean"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "synthetic_traceroute_hops{target=\"*******\"}", "interval": "", "legendFormat": "{{worker_id}} network", "refId": "A"}], "title": "Traceroute Hop Count Over Time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Current hop count for each network probe", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 30, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 15}, {"color": "red", "value": 25}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}, "id": 3, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "synthetic_traceroute_hops{target=\"*******\"}", "interval": "", "legendFormat": "{{worker_id}}", "refId": "A"}], "title": "Current Hop Count by Network", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Simple network topology showing connections from each probe to *******", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 20}, "id": 4, "options": {"edges": {"mainStatUnit": "short", "secondaryStatUnit": "short"}, "nodes": {"mainStatUnit": "short", "secondaryStatUnit": "short"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "synthetic_traceroute_hops{target=\"*******\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Network Topology - Probe to ******* Connections", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "instance": true, "job": true, "network": true, "target": true}, "indexByName": {}, "renameByName": {"Value": "mainStat", "worker_id": "id"}}}, {"id": "calculateField", "options": {"alias": "title", "binary": {"left": "id", "operator": "+", "reducer": "sum", "right": " Network"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "calculateField", "options": {"alias": "arc__*******", "binary": {"left": "mainStat", "operator": "*", "reducer": "sum", "right": "1"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "reduce", "options": {"includeTimeField": false, "mode": "seriesToRows", "reducers": ["lastNotNull"]}}], "type": "nodeGraph"}], "refresh": "30s", "schemaVersion": 36, "style": "dark", "tags": ["network", "topology", "traceroute", "node-graph"], "templating": {"list": []}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Network Topology Dashboard", "uid": "network-topology", "version": 1, "weekStart": ""}